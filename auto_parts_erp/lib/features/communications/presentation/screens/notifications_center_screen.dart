import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/core/services/advanced_notification_service.dart';

class NotificationsCenterScreen extends ConsumerStatefulWidget {
  const NotificationsCenterScreen({super.key});

  @override
  ConsumerState<NotificationsCenterScreen> createState() => _NotificationsCenterScreenState();
}

class _NotificationsCenterScreenState extends ConsumerState<NotificationsCenterScreen> {
  String _selectedFilter = 'all';
  final List<String> _filterOptions = ['all', 'unread', 'inventory', 'sales', 'system'];

  @override
  Widget build(BuildContext context) {
    final notifications = ref.watch(notificationsStreamProvider);
    final unreadCount = ref.watch(unreadNotificationsCountProvider);

    return ScaffoldPage(
      header: PageHeader(
        title: Row(
          children: [
            Text(AppLocalizations.of(context).translate('notificationsCenter')),
            const SizedBox(width: 8),
            unreadCount.when(
              data: (count) => count > 0 
                  ? Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        count.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    )
                  : const SizedBox(),
              loading: () => const SizedBox(),
              error: (_, __) => const SizedBox(),
            ),
          ],
        ),
        commandBar: Row(
          children: [
            ComboBox<String>(
              value: _selectedFilter,
              items: _filterOptions.map((filter) => ComboBoxItem<String>(
                value: filter,
                child: Text(_getFilterDisplayName(filter)),
              )).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedFilter = value;
                  });
                }
              },
            ),
            const SizedBox(width: 8),
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.read),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('markAllRead')),
                ],
              ),
              onPressed: () {
                ref.read(advancedNotificationServiceProvider).markAllAsRead();
              },
            ),
            const SizedBox(width: 8),
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.settings),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('settings')),
                ],
              ),
              onPressed: () {
                _showNotificationSettings();
              },
            ),
          ],
        ),
      ),
      content: notifications.when(
        data: (notificationsList) {
          final filteredNotifications = _filterNotifications(notificationsList);
          
          if (filteredNotifications.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(FluentIcons.inbox, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context).translate('noNotifications'),
                    style: FluentTheme.of(context).typography.subtitle,
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: filteredNotifications.length,
            itemBuilder: (context, index) {
              final notification = filteredNotifications[index];
              return _buildNotificationItem(notification);
            },
          );
        },
        loading: () => const Center(child: ProgressRing()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(FluentIcons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context).translate('errorLoadingNotifications'),
                style: FluentTheme.of(context).typography.subtitle,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: FluentTheme.of(context).typography.body,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<AppNotification> _filterNotifications(List<AppNotification> notifications) {
    switch (_selectedFilter) {
      case 'unread':
        return notifications.where((n) => !n.isRead).toList();
      case 'inventory':
        return notifications.where((n) => n.type == NotificationType.lowStock).toList();
      case 'sales':
        return notifications.where((n) => 
          n.type == NotificationType.orderOverdue || 
          n.type == NotificationType.paymentOverdue
        ).toList();
      case 'system':
        return notifications.where((n) => 
          n.type == NotificationType.systemMaintenance || 
          n.type == NotificationType.securityAlert
        ).toList();
      default:
        return notifications;
    }
  }

  Widget _buildNotificationItem(AppNotification notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Card(
        backgroundColor: notification.isRead 
            ? null 
            : FluentTheme.of(context).accentColor.withValues(alpha: 0.05),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getNotificationIcon(notification.type),
                    size: 20,
                    color: _getPriorityColor(notification.priority),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      notification.title,
                      style: FluentTheme.of(context).typography.body?.copyWith(
                        fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
                      ),
                    ),
                  ),
                  if (!notification.isRead)
                    Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Colors.blue,
                        shape: BoxShape.circle,
                      ),
                    ),
                  const SizedBox(width: 8),
                  Text(
                    _formatDateTime(notification.createdAt),
                    style: FluentTheme.of(context).typography.caption,
                  ),
                  const SizedBox(width: 8),
                  MenuFlyout(
                    content: (context) => [
                      MenuFlyoutItem(
                        leading: const Icon(FluentIcons.read),
                        text: Text(notification.isRead 
                            ? AppLocalizations.of(context).translate('markUnread')
                            : AppLocalizations.of(context).translate('markRead')),
                        onPressed: () {
                          if (!notification.isRead) {
                            ref.read(advancedNotificationServiceProvider).markAsRead(notification.id);
                          }
                        },
                      ),
                      MenuFlyoutItem(
                        leading: const Icon(FluentIcons.delete),
                        text: Text(AppLocalizations.of(context).translate('delete')),
                        onPressed: () {
                          ref.read(advancedNotificationServiceProvider).deleteNotification(notification.id);
                        },
                      ),
                    ],
                    child: IconButton(
                      icon: const Icon(FluentIcons.more),
                      onPressed: () {},
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                notification.message,
                style: FluentTheme.of(context).typography.body,
              ),
              if (notification.actionButtons.isNotEmpty) ...[
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  children: notification.actionButtons.map((action) => Button(
                    child: Text(action.title),
                    onPressed: () {
                      ref.read(advancedNotificationServiceProvider).handleNotificationAction(
                        notification.id,
                        action.id,
                      );
                    },
                  )).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.lowStock:
        return FluentIcons.package;
      case NotificationType.orderOverdue:
        return FluentIcons.clock;
      case NotificationType.paymentOverdue:
        return FluentIcons.money;
      case NotificationType.systemMaintenance:
        return FluentIcons.settings;
      case NotificationType.reminder:
        return FluentIcons.reminder;
      case NotificationType.securityAlert:
        return FluentIcons.shield;
      case NotificationType.newMessage:
        return FluentIcons.mail;
    }
  }

  Color _getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Colors.grey;
      case NotificationPriority.medium:
        return Colors.blue;
      case NotificationPriority.high:
        return Colors.orange;
      case NotificationPriority.critical:
        return Colors.red;
    }
  }

  String _getFilterDisplayName(String filter) {
    switch (filter) {
      case 'all':
        return AppLocalizations.of(context).translate('all');
      case 'unread':
        return AppLocalizations.of(context).translate('unread');
      case 'inventory':
        return AppLocalizations.of(context).translate('inventory');
      case 'sales':
        return AppLocalizations.of(context).translate('sales');
      case 'system':
        return AppLocalizations.of(context).translate('system');
      default:
        return filter;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return AppLocalizations.of(context).translate('justNow');
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  void _showNotificationSettings() {
    showDialog(
      context: context,
      builder: (context) => ContentDialog(
        title: Text(AppLocalizations.of(context).translate('notificationSettings')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(AppLocalizations.of(context).translate('notificationSettingsDescription')),
            const SizedBox(height: 16),
            // Add notification settings UI here
          ],
        ),
        actions: [
          Button(
            child: Text(AppLocalizations.of(context).translate('cancel')),
            onPressed: () => Navigator.of(context).pop(),
          ),
          FilledButton(
            child: Text(AppLocalizations.of(context).translate('save')),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}
