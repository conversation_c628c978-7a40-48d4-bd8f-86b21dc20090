import 'package:auto_parts_erp/features/sales/data/models/sales_model.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class SalesPOSScreen extends StatefulWidget {
  const SalesPOSScreen({super.key});

  @override
  State<SalesPOSScreen> createState() => _SalesPOSScreenState();
}

class _SalesPOSScreenState extends State<SalesPOSScreen> {
  List<SalesItem> salesItems = <SalesItem>[];
  late SalesDataSource salesDataSource;

  @override
  void initState() {
    super.initState();
    populateData();
  }

  void populateData() {
    salesItems = <SalesItem>[
      SalesItem(id: '1', productName: 'Brake Pad', quantity: 2, price: 50.00, total: 100.00),
      SalesItem(id: '2', productName: 'Spark Plug', quantity: 4, price: 10.00, total: 40.00),
    ];
    salesDataSource = SalesDataSource(salesItems: salesItems);
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldPage(
      header: const PageHeader(title: Text('Sales')),
      content: Column(
        children: [
          Row(
            children: [
              const Expanded(child: TextBox(placeholder: 'Search Products or Scan Barcode')),
              const SizedBox(width: 10),
              FilledButton(child: const Text('Add Customer'), onPressed: () {}),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: Row(
              children: [
                // Cart/Selected Items
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      const Text('Selected Items', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 10),
                      Expanded(
                        child: SfDataGrid(
                          source: salesDataSource,
                          columnWidthMode: ColumnWidthMode.fill,
                          columns: <GridColumn>[
                            GridColumn(columnName: 'productName', label: const Text('Product')),
                            GridColumn(columnName: 'quantity', label: const Text('Qty')),
                            GridColumn(columnName: 'price', label: const Text('Price')),
                            GridColumn(columnName: 'total', label: const Text('Total')),
                            GridColumn(
                              columnName: 'actions',
                              label: const Text('Actions'),
                              columnWidthMode: ColumnWidthMode.auto,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Total:', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                          Text('100.00', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const SizedBox(height: 20),
                      FilledButton(child: const Text('Process Sale'), onPressed: () {}),
                    ],
                  ),
                ),
                const SizedBox(width: 20),
                // Customer & Payment
                Expanded(
                  flex: 1,
                  child: Column(
                    children: [
                      const Text('Customer Details', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 10),
                      const TextBox(placeholder: 'Customer Name'),
                      const SizedBox(height: 10),
                      const TextBox(placeholder: 'Phone Number'),
                      const SizedBox(height: 20),
                      const Text('Payment Method', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 10),
                      ComboBox<String>(
                        placeholder: const Text('Select Payment Method'),
                        items: const [
                          ComboBoxItem(value: 'cash', child: Text('Cash')),
                          ComboBoxItem(value: 'card', child: Text('Card')),
                          ComboBoxItem(value: 'bank_transfer', child: Text('Bank Transfer')),
                        ],
                        onChanged: (value) {},
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class SalesDataSource extends DataGridSource {
  SalesDataSource({required List<SalesItem> salesItems}) {
    _salesItems = salesItems
        .map<DataGridRow>((
          e) => DataGridRow(cells: [
              DataGridCell<String>(columnName: 'productName', value: e.productName),
              DataGridCell<int>(columnName: 'quantity', value: e.quantity),
              DataGridCell<double>(columnName: 'price', value: e.price),
              DataGridCell<double>(columnName: 'total', value: e.total),
              DataGridCell<Widget>(columnName: 'actions', value: IconButton(icon: const Icon(FluentIcons.delete), onPressed: () {})),
            ]))
        .toList();
  }

  List<DataGridRow> _salesItems = [];

  @override
  List<DataGridRow> get rows => _salesItems;

  @override
  DataGridRowAdapter? buildRow(DataGridRow row) {
    return DataGridRowAdapter(
        cells: row.getCells().map<Widget>((
      e) {
      return Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.all(8.0),
        child: e.value is Widget ? e.value : Text(e.value.toString()),
      );
    }).toList());
  }
}