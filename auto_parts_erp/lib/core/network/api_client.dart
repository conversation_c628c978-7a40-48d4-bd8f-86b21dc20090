import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/api_constants.dart';

// Dio provider
final dioProvider = Provider<Dio>((ref) {
  final dio = Dio();
  
  // Base configuration
  dio.options.baseUrl = ApiConstants.baseUrl;
  dio.options.connectTimeout = const Duration(seconds: 30);
  dio.options.receiveTimeout = const Duration(seconds: 30);
  dio.options.sendTimeout = const Duration(seconds: 30);
  
  // Add interceptors
  dio.interceptors.add(LogInterceptor(
    requestBody: true,
    responseBody: true,
    requestHeader: true,
    responseHeader: false,
  ));
  
  // Add auth interceptor
  dio.interceptors.add(AuthInterceptor());
  
  return dio;
});

// API Client provider
final apiClientProvider = Provider<ApiClient>((ref) {
  final dio = ref.watch(dioProvider);
  return ApiClient(dio);
});

class ApiClient {
  final Dio _dio;
  
  ApiClient(this._dio);
  
  // Auth endpoints
  Future<Response> login(String username, String password) {
    return _dio.post('/auth/login', data: {
      'username': username,
      'password': password,
    });
  }
  
  Future<Response> refreshToken(String refreshToken) {
    return _dio.post('/auth/refresh', data: {
      'refresh_token': refreshToken,
    });
  }
  
  // Products endpoints
  Future<Response> getProducts({int page = 1, int limit = 50, String? search}) {
    return _dio.get('/products', queryParameters: {
      'page': page,
      'limit': limit,
      if (search != null) 'search': search,
    });
  }
  
  Future<Response> getProduct(int id) {
    return _dio.get('/products/$id');
  }
  
  Future<Response> createProduct(Map<String, dynamic> data) {
    return _dio.post('/products', data: data);
  }
  
  Future<Response> updateProduct(int id, Map<String, dynamic> data) {
    return _dio.put('/products/$id', data: data);
  }
  
  Future<Response> deleteProduct(int id) {
    return _dio.delete('/products/$id');
  }
  
  // Inventory endpoints
  Future<Response> getInventory({int? branchId}) {
    return _dio.get('/inventory', queryParameters: {
      if (branchId != null) 'branch_id': branchId,
    });
  }
  
  Future<Response> updateInventory(int productId, int branchId, int quantity) {
    return _dio.put('/inventory', data: {
      'product_id': productId,
      'branch_id': branchId,
      'quantity': quantity,
    });
  }
  
  // Sales endpoints
  Future<Response> getSales({DateTime? startDate, DateTime? endDate}) {
    return _dio.get('/sales', queryParameters: {
      if (startDate != null) 'start_date': startDate.toIso8601String(),
      if (endDate != null) 'end_date': endDate.toIso8601String(),
    });
  }
  
  Future<Response> createSale(Map<String, dynamic> data) {
    return _dio.post('/sales', data: data);
  }
  
  // Sync endpoints
  Future<Response> syncData(List<Map<String, dynamic>> data) {
    return _dio.post('/sync', data: {'operations': data});
  }
  
  Future<Response> pullUpdates(DateTime lastSync) {
    return _dio.get('/sync/pull', queryParameters: {
      'last_sync': lastSync.toIso8601String(),
    });
  }
}

class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Add auth token if available
    final token = _getStoredToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      // Handle token expiration
      _handleTokenExpiration();
    }
    handler.next(err);
  }
  
  String? _getStoredToken() {
    // TODO: Implement token storage/retrieval
    return null;
  }
  
  void _handleTokenExpiration() {
    // TODO: Implement token refresh or redirect to login
  }
}
