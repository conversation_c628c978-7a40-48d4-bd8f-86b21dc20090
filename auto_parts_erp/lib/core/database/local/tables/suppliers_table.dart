import 'package:drift/drift.dart';

@DataClassName('Supplier')
class Suppliers extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(min: 2, max: 100)();
  TextColumn get contactPerson => text().nullable()();
  TextColumn get email => text().nullable()();
  TextColumn get phone => text().nullable()();
  TextColumn get address => text().nullable()();
  TextColumn get city => text().nullable()();
  TextColumn get country => text().nullable()();
  TextColumn get taxNumber => text().nullable()();
  TextColumn get website => text().nullable()();
  RealColumn get currentBalance => real().withDefault(const Constant(0.0))();
  IntColumn get paymentTerms => integer().withDefault(const Constant(30))(); // Days
  RealColumn get rating => real().withDefault(const Constant(0.0))(); // 0-5 stars
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  TextColumn get notes => text().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().nullable()();
}
