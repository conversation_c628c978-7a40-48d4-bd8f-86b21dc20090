import 'dart:io';
import 'package:postgres/postgres.dart';
import 'package:dotenv/dotenv.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  static DatabaseService get instance => _instance;
  
  DatabaseService._internal();
  
  late Connection _connection;
  Connection get connection => _connection;
  
  Future<void> initialize() async {
    final env = DotEnv()..load();
    
    final databaseUrl = env['DATABASE_URL'] ?? 
        'postgres://user:password@localhost:5432/auto_parts_erp';
    
    try {
      _connection = await Connection.open(
        Endpoint(
          host: _extractHost(databaseUrl),
          port: _extractPort(databaseUrl),
          database: _extractDatabase(databaseUrl),
          username: _extractUsername(databaseUrl),
          password: _extractPassword(databaseUrl),
        ),
        settings: ConnectionSettings(
          sslMode: SslMode.prefer,
          connectTimeout: Duration(seconds: 30),
        ),
      );
      
      print('✅ Database connected successfully');
      
      // Run migrations if needed
      await _runMigrations();
      
    } catch (e) {
      print('❌ Database connection failed: $e');
      exit(1);
    }
  }
  
  Future<void> _runMigrations() async {
    try {
      // Check if migrations table exists
      final result = await _connection.execute(
        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'migrations')"
      );
      
      if (result.isEmpty || result.first.first == false) {
        // Create migrations table
        await _connection.execute('''
          CREATE TABLE migrations (
            id SERIAL PRIMARY KEY,
            version VARCHAR(255) NOT NULL UNIQUE,
            executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          )
        ''');
        
        // Run initial migration
        await _runInitialMigration();
        
        // Mark migration as executed
        await _connection.execute(
          "INSERT INTO migrations (version) VALUES ('001_initial')"
        );
        
        print('✅ Initial migration completed');
      }
    } catch (e) {
      print('⚠️  Migration error: $e');
    }
  }
  
  Future<void> _runInitialMigration() async {
    // Read and execute init.sql
    final initSql = await File('init.sql').readAsString();
    final statements = initSql.split(';').where((s) => s.trim().isNotEmpty);
    
    for (final statement in statements) {
      try {
        await _connection.execute(statement.trim());
      } catch (e) {
        print('⚠️  SQL execution error: $e');
        print('Statement: $statement');
      }
    }
  }
  
  String _extractHost(String url) {
    final uri = Uri.parse(url);
    return uri.host;
  }
  
  int _extractPort(String url) {
    final uri = Uri.parse(url);
    return uri.port;
  }
  
  String _extractDatabase(String url) {
    final uri = Uri.parse(url);
    return uri.pathSegments.isNotEmpty ? uri.pathSegments.first : 'auto_parts_erp';
  }
  
  String _extractUsername(String url) {
    final uri = Uri.parse(url);
    return uri.userInfo.split(':').first;
  }
  
  String _extractPassword(String url) {
    final uri = Uri.parse(url);
    final userInfo = uri.userInfo.split(':');
    return userInfo.length > 1 ? userInfo[1] : '';
  }
  
  Future<void> close() async {
    await _connection.close();
  }
  
  // Helper methods for common database operations
  Future<List<Map<String, dynamic>>> query(
    String sql, [
    Map<String, dynamic>? parameters,
  ]) async {
    try {
      final result = await _connection.execute(
        Sql.named(sql),
        parameters: parameters,
      );
      
      return result.map((row) {
        final map = <String, dynamic>{};
        for (int i = 0; i < row.length; i++) {
          map[result.schema[i].columnName] = row[i];
        }
        return map;
      }).toList();
    } catch (e) {
      print('Query error: $e');
      print('SQL: $sql');
      print('Parameters: $parameters');
      rethrow;
    }
  }
  
  Future<Map<String, dynamic>?> queryOne(
    String sql, [
    Map<String, dynamic>? parameters,
  ]) async {
    final results = await query(sql, parameters);
    return results.isNotEmpty ? results.first : null;
  }
  
  Future<int> execute(
    String sql, [
    Map<String, dynamic>? parameters,
  ]) async {
    try {
      final result = await _connection.execute(
        Sql.named(sql),
        parameters: parameters,
      );
      return result.affectedRows;
    } catch (e) {
      print('Execute error: $e');
      print('SQL: $sql');
      print('Parameters: $parameters');
      rethrow;
    }
  }
  
  Future<T> transaction<T>(Future<T> Function() action) async {
    await _connection.execute('BEGIN');
    try {
      final result = await action();
      await _connection.execute('COMMIT');
      return result;
    } catch (e) {
      await _connection.execute('ROLLBACK');
      rethrow;
    }
  }
}
