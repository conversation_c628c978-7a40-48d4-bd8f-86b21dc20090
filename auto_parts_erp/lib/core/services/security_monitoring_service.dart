import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../database/database_provider.dart';
import '../constants/app_constants.dart';

// Security monitoring service provider
final securityMonitoringServiceProvider = Provider<SecurityMonitoringService>((ref) {
  final database = ref.watch(databaseProvider);
  return SecurityMonitoringService(database);
});

// Security alerts provider
final securityAlertsProvider = StreamProvider<List<SecurityAlert>>((ref) {
  final service = ref.watch(securityMonitoringServiceProvider);
  return service.alertsStream;
});

// Security stats provider
final securityStatsProvider = FutureProvider<SecurityStats>((ref) async {
  final service = ref.watch(securityMonitoringServiceProvider);
  return await service.getSecurityStats();
});

class SecurityMonitoringService {
  final AppDatabase _database;
  final StreamController<List<SecurityAlert>> _alertsController = StreamController.broadcast();
  late Timer _monitoringTimer;
  final List<SecurityAlert> _activeAlerts = [];

  SecurityMonitoringService(this._database) {
    _startMonitoring();
  }

  Stream<List<SecurityAlert>> get alertsStream => _alertsController.stream;

  void _startMonitoring() {
    // Monitor security events every 30 seconds
    _monitoringTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _performSecurityChecks();
    });
  }

  Future<void> _performSecurityChecks() async {
    try {
      // Check for suspicious login attempts
      await _checkSuspiciousLogins();
      
      // Check for unusual data access patterns
      await _checkDataAccessPatterns();
      
      // Check for failed authentication attempts
      await _checkFailedAuthentications();
      
      // Check for privilege escalation attempts
      await _checkPrivilegeEscalation();
      
      // Check for data export activities
      await _checkDataExports();
      
      // Update alerts stream
      _alertsController.add(List.from(_activeAlerts));
    } catch (e) {
      // TODO: Implement proper logging
    }
  }

  Future<void> _checkSuspiciousLogins() async {
    // Mock implementation - replace with actual database calls
    final recentLogins = <LoginAttempt>[];
    
    // Group logins by user and check for unusual patterns
    final loginsByUser = <int, List<LoginAttempt>>{};
    for (final login in recentLogins) {
      loginsByUser.putIfAbsent(login.userId, () => []).add(login);
    }
    
    for (final entry in loginsByUser.entries) {
      final userId = entry.key;
      final logins = entry.value;
      
      // Check for multiple logins from different locations
      final uniqueIPs = logins.map((l) => l.ipAddress).toSet();
      if (uniqueIPs.length > 3) {
        _addAlert(SecurityAlert(
          id: 'suspicious_login_${userId}_${DateTime.now().millisecondsSinceEpoch}',
          type: SecurityAlertType.suspiciousLogin,
          severity: SecuritySeverity.high,
          title: 'Multiple login locations detected',
          description: 'User $userId logged in from ${uniqueIPs.length} different IP addresses in the last hour',
          userId: userId,
          timestamp: DateTime.now(),
          metadata: {'ip_addresses': uniqueIPs.toList()},
        ));
      }
      
      // Check for rapid successive logins
      if (logins.length > 10) {
        _addAlert(SecurityAlert(
          id: 'rapid_login_${userId}_${DateTime.now().millisecondsSinceEpoch}',
          type: SecurityAlertType.rapidLogins,
          severity: SecuritySeverity.medium,
          title: 'Rapid login attempts detected',
          description: 'User $userId had ${logins.length} login attempts in the last hour',
          userId: userId,
          timestamp: DateTime.now(),
          metadata: {'login_count': logins.length},
        ));
      }
    }
  }

  Future<void> _checkDataAccessPatterns() async {
    // Mock implementation - replace with actual database calls
    final recentAccess = <DataAccessLog>[];
    
    // Group access by user
    final accessByUser = <int, List<DataAccessLog>>{};
    for (final access in recentAccess) {
      accessByUser.putIfAbsent(access.userId, () => []).add(access);
    }
    
    for (final entry in accessByUser.entries) {
      final userId = entry.key;
      final accesses = entry.value;
      
      // Check for unusual volume of data access
      if (accesses.length > 100) {
        _addAlert(SecurityAlert(
          id: 'high_data_access_${userId}_${DateTime.now().millisecondsSinceEpoch}',
          type: SecurityAlertType.unusualDataAccess,
          severity: SecuritySeverity.medium,
          title: 'High volume data access detected',
          description: 'User $userId accessed ${accesses.length} records in the last 2 hours',
          userId: userId,
          timestamp: DateTime.now(),
          metadata: {'access_count': accesses.length},
        ));
      }
      
      // Check for access to sensitive data outside business hours
      final sensitiveAccess = accesses.where((a) => a.isSensitive).toList();
      final outsideHours = sensitiveAccess.where((a) => _isOutsideBusinessHours(a.timestamp)).toList();
      
      if (outsideHours.isNotEmpty) {
        _addAlert(SecurityAlert(
          id: 'after_hours_access_${userId}_${DateTime.now().millisecondsSinceEpoch}',
          type: SecurityAlertType.afterHoursAccess,
          severity: SecuritySeverity.high,
          title: 'After-hours sensitive data access',
          description: 'User $userId accessed sensitive data outside business hours',
          userId: userId,
          timestamp: DateTime.now(),
          metadata: {'sensitive_access_count': outsideHours.length},
        ));
      }
    }
  }

  Future<void> _checkFailedAuthentications() async {
    // Mock implementation - replace with actual database calls
    final failedAttempts = <FailedAuthAttempt>[];
    
    // Group by IP address
    final attemptsByIP = <String, List<FailedAuthAttempt>>{};
    for (final attempt in failedAttempts) {
      attemptsByIP.putIfAbsent(attempt.ipAddress, () => []).add(attempt);
    }
    
    for (final entry in attemptsByIP.entries) {
      final ipAddress = entry.key;
      final attempts = entry.value;
      
      // Check for brute force attacks
      if (attempts.length > 5) {
        _addAlert(SecurityAlert(
          id: 'brute_force_${ipAddress}_${DateTime.now().millisecondsSinceEpoch}',
          type: SecurityAlertType.bruteForceAttempt,
          severity: SecuritySeverity.critical,
          title: 'Potential brute force attack',
          description: 'Multiple failed login attempts from IP $ipAddress',
          timestamp: DateTime.now(),
          metadata: {
            'ip_address': ipAddress,
            'attempt_count': attempts.length,
            'usernames_tried': attempts.map((a) => a.username).toSet().toList(),
          },
        ));
      }
    }
  }

  Future<void> _checkPrivilegeEscalation() async {
    // Mock implementation - replace with actual database calls
    final privilegeChanges = <PrivilegeChange>[];
    
    for (final change in privilegeChanges) {
      if (change.isEscalation) {
        _addAlert(SecurityAlert(
          id: 'privilege_escalation_${change.userId}_${DateTime.now().millisecondsSinceEpoch}',
          type: SecurityAlertType.privilegeEscalation,
          severity: SecuritySeverity.high,
          title: 'Privilege escalation detected',
          description: 'User ${change.userId} privileges were elevated',
          userId: change.userId,
          timestamp: DateTime.now(),
          metadata: {
            'old_role': change.oldRole,
            'new_role': change.newRole,
            'changed_by': change.changedBy,
          },
        ));
      }
    }
  }

  Future<void> _checkDataExports() async {
    // Mock implementation - replace with actual database calls
    final exports = <DataExport>[];
    
    for (final export in exports) {
      if (export.recordCount > 1000) {
        _addAlert(SecurityAlert(
          id: 'large_export_${export.userId}_${DateTime.now().millisecondsSinceEpoch}',
          type: SecurityAlertType.largeDataExport,
          severity: SecuritySeverity.medium,
          title: 'Large data export detected',
          description: 'User ${export.userId} exported ${export.recordCount} records',
          userId: export.userId,
          timestamp: DateTime.now(),
          metadata: {
            'record_count': export.recordCount,
            'export_type': export.exportType,
            'file_size': export.fileSize,
          },
        ));
      }
    }
  }

  void _addAlert(SecurityAlert alert) {
    // Check if similar alert already exists
    final existingAlert = _activeAlerts.firstWhere(
      (a) => a.type == alert.type && a.userId == alert.userId,
      orElse: () => SecurityAlert.empty(),
    );
    
    if (existingAlert.id.isEmpty) {
      _activeAlerts.add(alert);
      _logSecurityEvent(alert);
    }
  }

  Future<void> _logSecurityEvent(SecurityAlert alert) async {
    await _database.logSecurityEvent(
      type: alert.type.toString(),
      severity: alert.severity.toString(),
      description: alert.description,
      userId: alert.userId,
      metadata: jsonEncode(alert.metadata),
    );
  }

  bool _isOutsideBusinessHours(DateTime timestamp) {
    final hour = timestamp.hour;
    return hour < 8 || hour > 18; // Outside 8 AM - 6 PM
  }

  Future<SecurityStats> getSecurityStats() async {
    // Mock implementation - replace with actual database calls
    final todayAlerts = 3;
    final weekAlerts = 12;
    final failedLogins = 5;
    final activeUsers = 25;

    return SecurityStats(
      alertsToday: todayAlerts,
      alertsThisWeek: weekAlerts,
      failedLoginsToday: failedLogins,
      activeUsersToday: activeUsers,
      securityScore: _calculateSecurityScore(todayAlerts, failedLogins),
    );
  }

  double _calculateSecurityScore(int alerts, int failedLogins) {
    // Simple security score calculation (0-100)
    double score = 100.0;
    
    // Deduct points for alerts
    score -= alerts * 5;
    
    // Deduct points for failed logins
    score -= failedLogins * 2;
    
    // Ensure score is between 0 and 100
    return score.clamp(0.0, 100.0);
  }

  Future<void> dismissAlert(String alertId) async {
    _activeAlerts.removeWhere((alert) => alert.id == alertId);
    _alertsController.add(List.from(_activeAlerts));
    
    await _database.dismissSecurityAlert(alertId);
  }

  Future<void> acknowledgeAlert(String alertId, String acknowledgedBy) async {
    final alertIndex = _activeAlerts.indexWhere((alert) => alert.id == alertId);
    if (alertIndex != -1) {
      _activeAlerts[alertIndex] = _activeAlerts[alertIndex].copyWith(
        isAcknowledged: true,
        acknowledgedBy: acknowledgedBy,
        acknowledgedAt: DateTime.now(),
      );
      _alertsController.add(List.from(_activeAlerts));
    }
    
    await _database.acknowledgeSecurityAlert(alertId, acknowledgedBy);
  }

  void dispose() {
    _monitoringTimer.cancel();
    _alertsController.close();
  }
}

// Data models
enum SecurityAlertType {
  suspiciousLogin,
  rapidLogins,
  unusualDataAccess,
  afterHoursAccess,
  bruteForceAttempt,
  privilegeEscalation,
  largeDataExport,
}

enum SecuritySeverity {
  low,
  medium,
  high,
  critical,
}

class SecurityAlert {
  final String id;
  final SecurityAlertType type;
  final SecuritySeverity severity;
  final String title;
  final String description;
  final int? userId;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;
  final bool isAcknowledged;
  final String? acknowledgedBy;
  final DateTime? acknowledgedAt;

  SecurityAlert({
    required this.id,
    required this.type,
    required this.severity,
    required this.title,
    required this.description,
    this.userId,
    required this.timestamp,
    this.metadata = const {},
    this.isAcknowledged = false,
    this.acknowledgedBy,
    this.acknowledgedAt,
  });

  SecurityAlert.empty()
      : id = '',
        type = SecurityAlertType.suspiciousLogin,
        severity = SecuritySeverity.low,
        title = '',
        description = '',
        userId = null,
        timestamp = DateTime.now(),
        metadata = const {},
        isAcknowledged = false,
        acknowledgedBy = null,
        acknowledgedAt = null;

  SecurityAlert copyWith({
    String? id,
    SecurityAlertType? type,
    SecuritySeverity? severity,
    String? title,
    String? description,
    int? userId,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
    bool? isAcknowledged,
    String? acknowledgedBy,
    DateTime? acknowledgedAt,
  }) {
    return SecurityAlert(
      id: id ?? this.id,
      type: type ?? this.type,
      severity: severity ?? this.severity,
      title: title ?? this.title,
      description: description ?? this.description,
      userId: userId ?? this.userId,
      timestamp: timestamp ?? this.timestamp,
      metadata: metadata ?? this.metadata,
      isAcknowledged: isAcknowledged ?? this.isAcknowledged,
      acknowledgedBy: acknowledgedBy ?? this.acknowledgedBy,
      acknowledgedAt: acknowledgedAt ?? this.acknowledgedAt,
    );
  }
}

class SecurityStats {
  final int alertsToday;
  final int alertsThisWeek;
  final int failedLoginsToday;
  final int activeUsersToday;
  final double securityScore;

  SecurityStats({
    required this.alertsToday,
    required this.alertsThisWeek,
    required this.failedLoginsToday,
    required this.activeUsersToday,
    required this.securityScore,
  });
}

class LoginAttempt {
  final int userId;
  final String ipAddress;
  final DateTime timestamp;
  final bool successful;

  LoginAttempt({
    required this.userId,
    required this.ipAddress,
    required this.timestamp,
    required this.successful,
  });
}

class DataAccessLog {
  final int userId;
  final String tableName;
  final String operation;
  final DateTime timestamp;
  final bool isSensitive;

  DataAccessLog({
    required this.userId,
    required this.tableName,
    required this.operation,
    required this.timestamp,
    required this.isSensitive,
  });
}

class FailedAuthAttempt {
  final String username;
  final String ipAddress;
  final DateTime timestamp;
  final String reason;

  FailedAuthAttempt({
    required this.username,
    required this.ipAddress,
    required this.timestamp,
    required this.reason,
  });
}

class PrivilegeChange {
  final int userId;
  final String oldRole;
  final String newRole;
  final int changedBy;
  final DateTime timestamp;
  final bool isEscalation;

  PrivilegeChange({
    required this.userId,
    required this.oldRole,
    required this.newRole,
    required this.changedBy,
    required this.timestamp,
    required this.isEscalation,
  });
}

class DataExport {
  final int userId;
  final String exportType;
  final int recordCount;
  final int fileSize;
  final DateTime timestamp;

  DataExport({
    required this.userId,
    required this.exportType,
    required this.recordCount,
    required this.fileSize,
    required this.timestamp,
  });
}
