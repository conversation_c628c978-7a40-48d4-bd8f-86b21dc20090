import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:drift/drift.dart';
import 'local/database.dart';

// Global database provider
final databaseProvider = Provider<AppDatabase>((ref) {
  print('🗄️ Creating AppDatabase instance...');
  return AppDatabase();
});

// User repository provider
final userRepositoryProvider = Provider<UserRepository>((ref) {
  final database = ref.watch(databaseProvider);
  return UserRepository(database);
});

// Product repository provider
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  final database = ref.watch(databaseProvider);
  return ProductRepository(database);
});

// Inventory repository provider
final inventoryRepositoryProvider = Provider<InventoryRepository>((ref) {
  final database = ref.watch(databaseProvider);
  return InventoryRepository(database);
});

// Sales repository provider
final salesRepositoryProvider = Provider<SalesRepository>((ref) {
  final database = ref.watch(databaseProvider);
  return SalesRepository(database);
});

// Repository classes
class UserRepository {
  final AppDatabase _database;
  
  UserRepository(this._database);
  
  Future<List<User>> getAllUsers() => _database.getAllUsers();
  Future<User?> getUserByUsername(String username) async {
    print('🔍 UserRepository: Searching for user with username: $username');
    final user = await _database.getUserByUsername(username);
    print('👤 UserRepository: Found user: ${user?.username}, passwordHash: ${user?.passwordHash}');
    return user;
  }
  Future<User?> getUserByEmail(String email) => _database.getUserByEmail(email);
  
  Future<int> createUser(UsersCompanion user) => _database.into(_database.users).insert(user);
  Future<bool> updateUser(int id, UsersCompanion user) async {
    final result = await (_database.update(_database.users)..where((u) => u.id.equals(id))).write(user);
    return result > 0;
  }

  Future<bool> deleteUser(int id) async {
    final result = await (_database.delete(_database.users)..where((u) => u.id.equals(id))).go();
    return result > 0;
  }
}

class ProductRepository {
  final AppDatabase _database;
  
  ProductRepository(this._database);
  
  Future<List<Product>> getAllProducts() => _database.getAllProducts();
  Future<Product?> getProductById(int id) => _database.getProductById(id);
  Future<List<Product>> searchProducts(String query) => _database.searchProducts(query);
  
  Future<int> createProduct(ProductsCompanion product) => _database.into(_database.products).insert(product);
  Future<bool> updateProduct(int id, ProductsCompanion product) async {
    final result = await (_database.update(_database.products)..where((p) => p.id.equals(id))).write(product);
    return result > 0;
  }

  Future<bool> deleteProduct(int id) async {
    final result = await (_database.delete(_database.products)..where((p) => p.id.equals(id))).go();
    return result > 0;
  }
      
  // OEM Numbers
  Future<List<ProductOemNumber>> getProductOemNumbers(int productId) =>
      (_database.select(_database.productOemNumbers)..where((o) => o.productId.equals(productId))).get();
  
  Future<int> addOemNumber(ProductOemNumbersCompanion oemNumber) => 
      _database.into(_database.productOemNumbers).insert(oemNumber);
      
  // Car Compatibility
  Future<List<ProductCarCompatibilityData>> getProductCarCompatibility(int productId) =>
      (_database.select(_database.productCarCompatibility)..where((c) => c.productId.equals(productId))).get();
  
  Future<int> addCarCompatibility(ProductCarCompatibilityCompanion compatibility) => 
      _database.into(_database.productCarCompatibility).insert(compatibility);
}

class InventoryRepository {
  final AppDatabase _database;
  
  InventoryRepository(this._database);
  
  Future<List<BranchInventoryData>> getInventoryByBranch(int branchId) => 
      _database.getInventoryByBranch(branchId);
  
  Future<BranchInventoryData?> getProductInventory(int productId, int branchId) =>
      (_database.select(_database.branchInventory)
        ..where((i) => i.productId.equals(productId) & i.branchId.equals(branchId)))
        .getSingleOrNull();
  
  Future<void> updateInventory(int productId, int branchId, int newQuantity) async {
    final existing = await getProductInventory(productId, branchId);
    
    if (existing != null) {
      await (_database.update(_database.branchInventory)
        ..where((i) => i.productId.equals(productId) & i.branchId.equals(branchId)))
        .write(BranchInventoryCompanion(
          quantity: Value(newQuantity),
          lastUpdated: Value(DateTime.now()),
        ));
    } else {
      await _database.into(_database.branchInventory).insert(
        BranchInventoryCompanion.insert(
          productId: productId,
          branchId: branchId,
          quantity: Value(newQuantity),
        ),
      );
    }
  }
  
  Future<void> recordMovement(InventoryMovementsCompanion movement) =>
      _database.into(_database.inventoryMovements).insert(movement);
}

class SalesRepository {
  final AppDatabase _database;
  
  SalesRepository(this._database);
  
  Future<List<Sale>> getAllSales() => _database.getAllSales();
  Future<List<Sale>> getSalesByDateRange(DateTime start, DateTime end) => 
      _database.getSalesByDateRange(start, end);
  
  Future<int> createSale(SalesCompanion sale) => _database.into(_database.sales).insert(sale);
  Future<int> createSaleItem(SalesItemsCompanion item) => _database.into(_database.salesItems).insert(item);
  
  Future<List<SalesItem>> getSaleItems(int saleId) =>
      (_database.select(_database.salesItems)..where((i) => i.saleId.equals(saleId))).get();

  Future<bool> updateSale(int id, SalesCompanion sale) async {
    final result = await (_database.update(_database.sales)..where((s) => s.id.equals(id))).write(sale);
    return result > 0;
  }

  Future<bool> deleteSale(int id) async {
    final result = await (_database.delete(_database.sales)..where((s) => s.id.equals(id))).go();
    return result > 0;
  }
}
