import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../../services/database_service.dart';
import '../middleware/auth_middleware.dart';

class PurchaseRoutes {
  Router get router {
    final router = Router();

    router.get('/', _getPurchases);
    router.get('/<id>', _getPurchase);
    router.post('/', _createPurchase);
    router.put('/<id>', _updatePurchase);
    router.delete('/<id>', _deletePurchase);

    return router;
  }

  Future<Response> _getPurchases(Request request) async {
    try {
      final page = int.tryParse(request.url.queryParameters['page'] ?? '1') ?? 1;
      final limit = int.tryParse(request.url.queryParameters['limit'] ?? '50') ?? 50;
      final offset = (page - 1) * limit;

      final purchases = await DatabaseService.instance.query('''
        SELECT p.*, s.name as supplier_name, b.name_en as branch_name, u.username
        FROM purchases p
        JOIN suppliers s ON p.supplier_id = s.id
        JOIN branches b ON p.branch_id = b.id
        JOIN users u ON p.user_id = u.id
        ORDER BY p.purchase_date DESC
        LIMIT @limit OFFSET @offset
      ''', {'limit': limit, 'offset': offset});

      return Response.ok(
        jsonEncode({'purchases': purchases}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to fetch purchases: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getPurchase(Request request) async {
    try {
      final id = int.tryParse(request.params['id']!);
      if (id == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid purchase ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final purchase = await DatabaseService.instance.queryOne('''
        SELECT p.*, s.name as supplier_name, b.name_en as branch_name, u.username
        FROM purchases p
        JOIN suppliers s ON p.supplier_id = s.id
        JOIN branches b ON p.branch_id = b.id
        JOIN users u ON p.user_id = u.id
        WHERE p.id = @id
      ''', {'id': id});

      if (purchase == null) {
        return Response.notFound(
          jsonEncode({'error': 'Purchase not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Get purchase items
      final items = await DatabaseService.instance.query('''
        SELECT pi.*, p.name_en as product_name, p.name_ar as product_name_ar
        FROM purchase_items pi
        JOIN products p ON pi.product_id = p.id
        WHERE pi.purchase_id = @purchase_id
      ''', {'purchase_id': id});

      purchase['items'] = items;

      return Response.ok(
        jsonEncode({'purchase': purchase}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to fetch purchase: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _createPurchase(Request request) async {
    try {
      if (!hasAnyRole(request, ['admin', 'manager'])) {
        return Response.forbidden(
          jsonEncode({'error': 'Insufficient permissions'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      final userInfo = getUserFromRequest(request);
      final items = data['items'] as List<dynamic>?;

      if (items == null || items.isEmpty) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Purchase items are required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final purchaseId = await DatabaseService.instance.transaction(() async {
        // Create purchase
        final purchaseResult = await DatabaseService.instance.queryOne('''
          INSERT INTO purchases (
            invoice_number, supplier_id, branch_id, user_id,
            delivery_date, subtotal, tax_amount, discount_amount, total_amount,
            paid_amount, remaining_amount, payment_method, status, notes
          ) VALUES (
            @invoice_number, @supplier_id, @branch_id, @user_id,
            @delivery_date, @subtotal, @tax_amount, @discount_amount, @total_amount,
            @paid_amount, @remaining_amount, @payment_method, @status, @notes
          ) RETURNING id
        ''', {
          'invoice_number': data['invoice_number'] ?? 'PUR-${DateTime.now().millisecondsSinceEpoch}',
          'supplier_id': data['supplier_id'],
          'branch_id': userInfo['branch_id'] ?? data['branch_id'],
          'user_id': userInfo['user_id'],
          'delivery_date': data['delivery_date'],
          'subtotal': data['subtotal'] ?? 0.0,
          'tax_amount': data['tax_amount'] ?? 0.0,
          'discount_amount': data['discount_amount'] ?? 0.0,
          'total_amount': data['total_amount'] ?? 0.0,
          'paid_amount': data['paid_amount'] ?? 0.0,
          'remaining_amount': data['remaining_amount'] ?? 0.0,
          'payment_method': data['payment_method'],
          'status': data['status'] ?? 'PENDING',
          'notes': data['notes'],
        });

        final purchaseId = purchaseResult!['id'];

        // Create purchase items
        for (final item in items) {
          await DatabaseService.instance.execute('''
            INSERT INTO purchase_items (
              purchase_id, product_id, quantity, unit_cost, total_cost,
              expiry_date, batch_number, notes
            ) VALUES (
              @purchase_id, @product_id, @quantity, @unit_cost, @total_cost,
              @expiry_date, @batch_number, @notes
            )
          ''', {
            'purchase_id': purchaseId,
            'product_id': item['product_id'],
            'quantity': item['quantity'],
            'unit_cost': item['unit_cost'],
            'total_cost': item['total_cost'],
            'expiry_date': item['expiry_date'],
            'batch_number': item['batch_number'],
            'notes': item['notes'],
          });

          // If status is RECEIVED, update inventory
          if (data['status'] == 'RECEIVED') {
            await DatabaseService.instance.execute('''
              INSERT INTO branch_inventory (product_id, branch_id, quantity, last_updated)
              VALUES (@product_id, @branch_id, @quantity, NOW())
              ON CONFLICT (product_id, branch_id)
              DO UPDATE SET quantity = branch_inventory.quantity + @quantity, last_updated = NOW()
            ''', {
              'product_id': item['product_id'],
              'branch_id': userInfo['branch_id'] ?? data['branch_id'],
              'quantity': item['quantity'],
            });

            // Record inventory movement
            await DatabaseService.instance.execute('''
              INSERT INTO inventory_movements (
                product_id, branch_id, movement_type, quantity,
                reference_type, reference_id, user_id, unit_cost
              ) VALUES (
                @product_id, @branch_id, 'IN', @quantity,
                'PURCHASE', @purchase_id, @user_id, @unit_cost
              )
            ''', {
              'product_id': item['product_id'],
              'branch_id': userInfo['branch_id'] ?? data['branch_id'],
              'quantity': item['quantity'],
              'purchase_id': purchaseId,
              'user_id': userInfo['user_id'],
              'unit_cost': item['unit_cost'],
            });
          }
        }

        return purchaseId;
      });

      return Response.ok(
        jsonEncode({
          'message': 'Purchase created successfully',
          'purchase_id': purchaseId,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to create purchase: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _updatePurchase(Request request) async {
    try {
      if (!hasAnyRole(request, ['admin', 'manager'])) {
        return Response.forbidden(
          jsonEncode({'error': 'Insufficient permissions'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final id = int.tryParse(request.params['id']!);
      if (id == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid purchase ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final affectedRows = await DatabaseService.instance.execute('''
        UPDATE purchases SET
          status = @status,
          delivery_date = @delivery_date,
          notes = @notes,
          updated_at = NOW()
        WHERE id = @id
      ''', {
        'id': id,
        'status': data['status'],
        'delivery_date': data['delivery_date'],
        'notes': data['notes'],
      });

      if (affectedRows == 0) {
        return Response.notFound(
          jsonEncode({'error': 'Purchase not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({'message': 'Purchase updated successfully'}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to update purchase: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _deletePurchase(Request request) async {
    try {
      if (!hasRole(request, 'admin')) {
        return Response.forbidden(
          jsonEncode({'error': 'Admin access required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final id = int.tryParse(request.params['id']!);
      if (id == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid purchase ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Update status to CANCELLED instead of deleting
      final affectedRows = await DatabaseService.instance.execute(
        'UPDATE purchases SET status = \'CANCELLED\', updated_at = NOW() WHERE id = @id',
        {'id': id},
      );

      if (affectedRows == 0) {
        return Response.notFound(
          jsonEncode({'error': 'Purchase not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({'message': 'Purchase cancelled successfully'}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to cancel purchase: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}
