import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../../services/database_service.dart';
import '../middleware/auth_middleware.dart';

class ProductRoutes {
  Router get router {
    final router = Router();

    router.get('/', _getProducts);
    router.get('/<id>', _getProduct);
    router.post('/', _createProduct);
    router.put('/<id>', _updateProduct);
    router.delete('/<id>', _deleteProduct);
    router.get('/search/<query>', _searchProducts);
    
    // OEM Numbers
    router.get('/<id>/oem-numbers', _getProductOemNumbers);
    router.post('/<id>/oem-numbers', _addProductOemNumber);
    router.delete('/<id>/oem-numbers/<oemId>', _deleteProductOemNumber);
    
    // Car Compatibility
    router.get('/<id>/car-compatibility', _getProductCarCompatibility);
    router.post('/<id>/car-compatibility', _addProductCarCompatibility);
    router.delete('/<id>/car-compatibility/<compatibilityId>', _deleteProductCarCompatibility);

    return router;
  }

  Future<Response> _getProducts(Request request) async {
    try {
      final page = int.tryParse(request.url.queryParameters['page'] ?? '1') ?? 1;
      final limit = int.tryParse(request.url.queryParameters['limit'] ?? '50') ?? 50;
      final search = request.url.queryParameters['search'];
      final categoryId = request.url.queryParameters['category_id'];
      final brandId = request.url.queryParameters['brand_id'];

      final offset = (page - 1) * limit;
      
      String whereClause = 'WHERE p.is_active = true';
      Map<String, dynamic> parameters = {'limit': limit, 'offset': offset};

      if (search != null && search.isNotEmpty) {
        whereClause += ' AND (p.name_en ILIKE @search OR p.name_ar ILIKE @search OR p.sku ILIKE @search)';
        parameters['search'] = '%$search%';
      }

      if (categoryId != null) {
        whereClause += ' AND p.category_id = @category_id';
        parameters['category_id'] = int.parse(categoryId);
      }

      if (brandId != null) {
        whereClause += ' AND p.brand_id = @brand_id';
        parameters['brand_id'] = int.parse(brandId);
      }

      final products = await DatabaseService.instance.query('''
        SELECT p.*, c.name_en as category_name, b.name_en as brand_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN brands b ON p.brand_id = b.id
        $whereClause
        ORDER BY p.created_at DESC
        LIMIT @limit OFFSET @offset
      ''', parameters);

      // Get total count
      final countResult = await DatabaseService.instance.queryOne('''
        SELECT COUNT(*) as total FROM products p $whereClause
      ''', parameters);

      final total = countResult?['total'] ?? 0;

      return Response.ok(
        jsonEncode({
          'products': products,
          'pagination': {
            'page': page,
            'limit': limit,
            'total': total,
            'pages': (total / limit).ceil(),
          },
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to fetch products: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getProduct(Request request) async {
    try {
      final id = int.tryParse(request.params['id']!);
      if (id == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid product ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final product = await DatabaseService.instance.queryOne('''
        SELECT p.*, c.name_en as category_name, b.name_en as brand_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN brands b ON p.brand_id = b.id
        WHERE p.id = @id
      ''', {'id': id});

      if (product == null) {
        return Response.notFound(
          jsonEncode({'error': 'Product not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Get OEM numbers
      final oemNumbers = await DatabaseService.instance.query(
        'SELECT * FROM product_oem_numbers WHERE product_id = @product_id',
        {'product_id': id},
      );

      // Get car compatibility
      final carCompatibility = await DatabaseService.instance.query(
        'SELECT * FROM product_car_compatibility WHERE product_id = @product_id',
        {'product_id': id},
      );

      product['oem_numbers'] = oemNumbers;
      product['car_compatibility'] = carCompatibility;

      return Response.ok(
        jsonEncode({'product': product}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to fetch product: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _createProduct(Request request) async {
    try {
      if (!hasAnyRole(request, ['admin', 'manager'])) {
        return Response.forbidden(
          jsonEncode({'error': 'Insufficient permissions'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      // Validate required fields
      if (data['name_en'] == null || data['name_ar'] == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Product names (English and Arabic) are required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final productId = await DatabaseService.instance.queryOne('''
        INSERT INTO products (
          name_en, name_ar, description_en, description_ar,
          sku, barcode, category_id, brand_id,
          cost_price, selling_price, weight, unit,
          min_stock_level, has_expiry_date
        ) VALUES (
          @name_en, @name_ar, @description_en, @description_ar,
          @sku, @barcode, @category_id, @brand_id,
          @cost_price, @selling_price, @weight, @unit,
          @min_stock_level, @has_expiry_date
        ) RETURNING id
      ''', {
        'name_en': data['name_en'],
        'name_ar': data['name_ar'],
        'description_en': data['description_en'],
        'description_ar': data['description_ar'],
        'sku': data['sku'],
        'barcode': data['barcode'],
        'category_id': data['category_id'],
        'brand_id': data['brand_id'],
        'cost_price': data['cost_price'] ?? 0.0,
        'selling_price': data['selling_price'] ?? 0.0,
        'weight': data['weight'],
        'unit': data['unit'] ?? 'piece',
        'min_stock_level': data['min_stock_level'] ?? 0,
        'has_expiry_date': data['has_expiry_date'] ?? false,
      });

      return Response.ok(
        jsonEncode({
          'message': 'Product created successfully',
          'product_id': productId?['id'],
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to create product: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _updateProduct(Request request) async {
    try {
      if (!hasAnyRole(request, ['admin', 'manager'])) {
        return Response.forbidden(
          jsonEncode({'error': 'Insufficient permissions'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final id = int.tryParse(request.params['id']!);
      if (id == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid product ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final affectedRows = await DatabaseService.instance.execute('''
        UPDATE products SET
          name_en = @name_en,
          name_ar = @name_ar,
          description_en = @description_en,
          description_ar = @description_ar,
          sku = @sku,
          barcode = @barcode,
          category_id = @category_id,
          brand_id = @brand_id,
          cost_price = @cost_price,
          selling_price = @selling_price,
          weight = @weight,
          unit = @unit,
          min_stock_level = @min_stock_level,
          has_expiry_date = @has_expiry_date,
          updated_at = NOW()
        WHERE id = @id
      ''', {
        ...data,
        'id': id,
      });

      if (affectedRows == 0) {
        return Response.notFound(
          jsonEncode({'error': 'Product not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({'message': 'Product updated successfully'}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to update product: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _deleteProduct(Request request) async {
    try {
      if (!hasRole(request, 'admin')) {
        return Response.forbidden(
          jsonEncode({'error': 'Admin access required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final id = int.tryParse(request.params['id']!);
      if (id == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid product ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Soft delete by setting is_active to false
      final affectedRows = await DatabaseService.instance.execute(
        'UPDATE products SET is_active = false, updated_at = NOW() WHERE id = @id',
        {'id': id},
      );

      if (affectedRows == 0) {
        return Response.notFound(
          jsonEncode({'error': 'Product not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({'message': 'Product deleted successfully'}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to delete product: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _searchProducts(Request request) async {
    try {
      final query = request.params['query']!;
      final limit = int.tryParse(request.url.queryParameters['limit'] ?? '20') ?? 20;

      final products = await DatabaseService.instance.query('''
        SELECT p.id, p.name_en, p.name_ar, p.sku, p.barcode, p.selling_price,
               c.name_en as category_name, b.name_en as brand_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN brands b ON p.brand_id = b.id
        WHERE p.is_active = true
        AND (
          p.name_en ILIKE @query OR 
          p.name_ar ILIKE @query OR 
          p.sku ILIKE @query OR 
          p.barcode ILIKE @query OR
          EXISTS (
            SELECT 1 FROM product_oem_numbers pon 
            WHERE pon.product_id = p.id AND pon.oem_number ILIKE @query
          )
        )
        ORDER BY p.name_en
        LIMIT @limit
      ''', {
        'query': '%$query%',
        'limit': limit,
      });

      return Response.ok(
        jsonEncode({'products': products}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Search failed: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // OEM Numbers methods
  Future<Response> _getProductOemNumbers(Request request) async {
    try {
      final productId = int.tryParse(request.params['id']!);
      if (productId == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid product ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final oemNumbers = await DatabaseService.instance.query(
        'SELECT * FROM product_oem_numbers WHERE product_id = @product_id ORDER BY is_primary DESC, oem_number',
        {'product_id': productId},
      );

      return Response.ok(
        jsonEncode({'oem_numbers': oemNumbers}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to fetch OEM numbers: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _addProductOemNumber(Request request) async {
    try {
      if (!hasAnyRole(request, ['admin', 'manager'])) {
        return Response.forbidden(
          jsonEncode({'error': 'Insufficient permissions'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final productId = int.tryParse(request.params['id']!);
      if (productId == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid product ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      if (data['oem_number'] == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'OEM number is required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      await DatabaseService.instance.execute('''
        INSERT INTO product_oem_numbers (product_id, oem_number, manufacturer, is_primary)
        VALUES (@product_id, @oem_number, @manufacturer, @is_primary)
      ''', {
        'product_id': productId,
        'oem_number': data['oem_number'],
        'manufacturer': data['manufacturer'],
        'is_primary': data['is_primary'] ?? false,
      });

      return Response.ok(
        jsonEncode({'message': 'OEM number added successfully'}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to add OEM number: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _deleteProductOemNumber(Request request) async {
    try {
      if (!hasAnyRole(request, ['admin', 'manager'])) {
        return Response.forbidden(
          jsonEncode({'error': 'Insufficient permissions'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final oemId = int.tryParse(request.params['oemId']!);
      if (oemId == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid OEM number ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final affectedRows = await DatabaseService.instance.execute(
        'DELETE FROM product_oem_numbers WHERE id = @id',
        {'id': oemId},
      );

      if (affectedRows == 0) {
        return Response.notFound(
          jsonEncode({'error': 'OEM number not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({'message': 'OEM number deleted successfully'}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to delete OEM number: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // Car Compatibility methods
  Future<Response> _getProductCarCompatibility(Request request) async {
    try {
      final productId = int.tryParse(request.params['id']!);
      if (productId == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid product ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final compatibility = await DatabaseService.instance.query(
        'SELECT * FROM product_car_compatibility WHERE product_id = @product_id ORDER BY car_make, car_model, year_from',
        {'product_id': productId},
      );

      return Response.ok(
        jsonEncode({'car_compatibility': compatibility}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to fetch car compatibility: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _addProductCarCompatibility(Request request) async {
    try {
      if (!hasAnyRole(request, ['admin', 'manager'])) {
        return Response.forbidden(
          jsonEncode({'error': 'Insufficient permissions'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final productId = int.tryParse(request.params['id']!);
      if (productId == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid product ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      if (data['car_make'] == null || data['car_model'] == null || data['year_from'] == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Car make, model, and year from are required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      await DatabaseService.instance.execute('''
        INSERT INTO product_car_compatibility (
          product_id, car_make, car_model, year_from, year_to, engine_type, notes
        ) VALUES (
          @product_id, @car_make, @car_model, @year_from, @year_to, @engine_type, @notes
        )
      ''', {
        'product_id': productId,
        'car_make': data['car_make'],
        'car_model': data['car_model'],
        'year_from': data['year_from'],
        'year_to': data['year_to'],
        'engine_type': data['engine_type'],
        'notes': data['notes'],
      });

      return Response.ok(
        jsonEncode({'message': 'Car compatibility added successfully'}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to add car compatibility: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _deleteProductCarCompatibility(Request request) async {
    try {
      if (!hasAnyRole(request, ['admin', 'manager'])) {
        return Response.forbidden(
          jsonEncode({'error': 'Insufficient permissions'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final compatibilityId = int.tryParse(request.params['compatibilityId']!);
      if (compatibilityId == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid compatibility ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final affectedRows = await DatabaseService.instance.execute(
        'DELETE FROM product_car_compatibility WHERE id = @id',
        {'id': compatibilityId},
      );

      if (affectedRows == 0) {
        return Response.notFound(
          jsonEncode({'error': 'Car compatibility not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({'message': 'Car compatibility deleted successfully'}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to delete car compatibility: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}
