import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../../services/database_service.dart';
import '../middleware/auth_middleware.dart';

class InventoryRoutes {
  Router get router {
    final router = Router();

    router.get('/', _getInventory);
    router.get('/branch/<branchId>', _getBranchInventory);
    router.put('/update', _updateInventory);
    router.post('/transfer', _transferInventory);
    router.get('/movements', _getInventoryMovements);
    router.get('/low-stock', _getLowStockItems);

    return router;
  }

  Future<Response> _getInventory(Request request) async {
    try {
      final branchId = request.url.queryParameters['branch_id'];
      
      String whereClause = '';
      Map<String, dynamic> parameters = {};

      if (branchId != null) {
        whereClause = 'WHERE bi.branch_id = @branch_id';
        parameters['branch_id'] = int.parse(branchId);
      }

      final inventory = await DatabaseService.instance.query('''
        SELECT bi.*, p.name_en, p.name_ar, p.sku, p.min_stock_level,
               b.name_en as branch_name
        FROM branch_inventory bi
        JOIN products p ON bi.product_id = p.id
        JOIN branches b ON bi.branch_id = b.id
        $whereClause
        ORDER BY p.name_en
      ''', parameters);

      return Response.ok(
        jsonEncode({'inventory': inventory}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to fetch inventory: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getBranchInventory(Request request) async {
    try {
      final branchId = int.tryParse(request.params['branchId']!);
      if (branchId == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid branch ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final inventory = await DatabaseService.instance.query('''
        SELECT bi.*, p.name_en, p.name_ar, p.sku, p.selling_price, p.min_stock_level
        FROM branch_inventory bi
        JOIN products p ON bi.product_id = p.id
        WHERE bi.branch_id = @branch_id AND p.is_active = true
        ORDER BY p.name_en
      ''', {'branch_id': branchId});

      return Response.ok(
        jsonEncode({'inventory': inventory}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to fetch branch inventory: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _updateInventory(Request request) async {
    try {
      if (!hasAnyRole(request, ['admin', 'manager', 'employee'])) {
        return Response.forbidden(
          jsonEncode({'error': 'Insufficient permissions'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final productId = data['product_id'];
      final branchId = data['branch_id'];
      final newQuantity = data['quantity'];
      final movementType = data['movement_type'] ?? 'ADJUSTMENT';
      final notes = data['notes'];

      if (productId == null || branchId == null || newQuantity == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Product ID, branch ID, and quantity are required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      await DatabaseService.instance.transaction(() async {
        // Get current quantity
        final currentInventory = await DatabaseService.instance.queryOne('''
          SELECT quantity FROM branch_inventory 
          WHERE product_id = @product_id AND branch_id = @branch_id
        ''', {'product_id': productId, 'branch_id': branchId});

        final currentQuantity = currentInventory?['quantity'] ?? 0;

        // Update or insert inventory
        await DatabaseService.instance.execute('''
          INSERT INTO branch_inventory (product_id, branch_id, quantity, last_updated)
          VALUES (@product_id, @branch_id, @quantity, NOW())
          ON CONFLICT (product_id, branch_id)
          DO UPDATE SET quantity = @quantity, last_updated = NOW()
        ''', {
          'product_id': productId,
          'branch_id': branchId,
          'quantity': newQuantity,
        });

        // Record movement
        final userInfo = getUserFromRequest(request);
        await DatabaseService.instance.execute('''
          INSERT INTO inventory_movements (
            product_id, branch_id, movement_type, quantity,
            quantity_before, quantity_after, notes, user_id
          ) VALUES (
            @product_id, @branch_id, @movement_type, @quantity,
            @quantity_before, @quantity_after, @notes, @user_id
          )
        ''', {
          'product_id': productId,
          'branch_id': branchId,
          'movement_type': movementType,
          'quantity': newQuantity - currentQuantity,
          'quantity_before': currentQuantity,
          'quantity_after': newQuantity,
          'notes': notes,
          'user_id': userInfo['user_id'],
        });
      });

      return Response.ok(
        jsonEncode({'message': 'Inventory updated successfully'}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to update inventory: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _transferInventory(Request request) async {
    try {
      if (!hasAnyRole(request, ['admin', 'manager'])) {
        return Response.forbidden(
          jsonEncode({'error': 'Insufficient permissions'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final productId = data['product_id'];
      final fromBranchId = data['from_branch_id'];
      final toBranchId = data['to_branch_id'];
      final quantity = data['quantity'];
      final notes = data['notes'];

      if (productId == null || fromBranchId == null || toBranchId == null || quantity == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'All transfer details are required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      if (quantity <= 0) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Quantity must be positive'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      await DatabaseService.instance.transaction(() async {
        // Check source inventory
        final sourceInventory = await DatabaseService.instance.queryOne('''
          SELECT quantity FROM branch_inventory 
          WHERE product_id = @product_id AND branch_id = @branch_id
        ''', {'product_id': productId, 'branch_id': fromBranchId});

        final sourceQuantity = sourceInventory?['quantity'] ?? 0;
        if (sourceQuantity < quantity) {
          throw Exception('Insufficient inventory in source branch');
        }

        // Update source branch inventory
        await DatabaseService.instance.execute('''
          UPDATE branch_inventory 
          SET quantity = quantity - @quantity, last_updated = NOW()
          WHERE product_id = @product_id AND branch_id = @branch_id
        ''', {
          'product_id': productId,
          'branch_id': fromBranchId,
          'quantity': quantity,
        });

        // Update destination branch inventory
        await DatabaseService.instance.execute('''
          INSERT INTO branch_inventory (product_id, branch_id, quantity, last_updated)
          VALUES (@product_id, @branch_id, @quantity, NOW())
          ON CONFLICT (product_id, branch_id)
          DO UPDATE SET quantity = branch_inventory.quantity + @quantity, last_updated = NOW()
        ''', {
          'product_id': productId,
          'branch_id': toBranchId,
          'quantity': quantity,
        });

        // Record movements
        final userInfo = getUserFromRequest(request);
        
        // OUT movement for source branch
        await DatabaseService.instance.execute('''
          INSERT INTO inventory_movements (
            product_id, branch_id, movement_type, quantity,
            quantity_before, quantity_after, notes, user_id
          ) VALUES (
            @product_id, @branch_id, 'OUT', @quantity,
            @quantity_before, @quantity_after, @notes, @user_id
          )
        ''', {
          'product_id': productId,
          'branch_id': fromBranchId,
          'quantity': -quantity,
          'quantity_before': sourceQuantity,
          'quantity_after': sourceQuantity - quantity,
          'notes': 'Transfer to branch $toBranchId: $notes',
          'user_id': userInfo['user_id'],
        });

        // IN movement for destination branch
        final destInventory = await DatabaseService.instance.queryOne('''
          SELECT quantity FROM branch_inventory 
          WHERE product_id = @product_id AND branch_id = @branch_id
        ''', {'product_id': productId, 'branch_id': toBranchId});

        final destQuantityBefore = (destInventory?['quantity'] ?? 0) - quantity;

        await DatabaseService.instance.execute('''
          INSERT INTO inventory_movements (
            product_id, branch_id, movement_type, quantity,
            quantity_before, quantity_after, notes, user_id
          ) VALUES (
            @product_id, @branch_id, 'IN', @quantity,
            @quantity_before, @quantity_after, @notes, @user_id
          )
        ''', {
          'product_id': productId,
          'branch_id': toBranchId,
          'quantity': quantity,
          'quantity_before': destQuantityBefore,
          'quantity_after': destQuantityBefore + quantity,
          'notes': 'Transfer from branch $fromBranchId: $notes',
          'user_id': userInfo['user_id'],
        });
      });

      return Response.ok(
        jsonEncode({'message': 'Inventory transferred successfully'}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to transfer inventory: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getInventoryMovements(Request request) async {
    try {
      final productId = request.url.queryParameters['product_id'];
      final branchId = request.url.queryParameters['branch_id'];
      final startDate = request.url.queryParameters['start_date'];
      final endDate = request.url.queryParameters['end_date'];
      final limit = int.tryParse(request.url.queryParameters['limit'] ?? '100') ?? 100;

      String whereClause = 'WHERE 1=1';
      Map<String, dynamic> parameters = {'limit': limit};

      if (productId != null) {
        whereClause += ' AND im.product_id = @product_id';
        parameters['product_id'] = int.parse(productId);
      }

      if (branchId != null) {
        whereClause += ' AND im.branch_id = @branch_id';
        parameters['branch_id'] = int.parse(branchId);
      }

      if (startDate != null) {
        whereClause += ' AND im.created_at >= @start_date';
        parameters['start_date'] = startDate;
      }

      if (endDate != null) {
        whereClause += ' AND im.created_at <= @end_date';
        parameters['end_date'] = endDate;
      }

      final movements = await DatabaseService.instance.query('''
        SELECT im.*, p.name_en as product_name, b.name_en as branch_name,
               u.username
        FROM inventory_movements im
        JOIN products p ON im.product_id = p.id
        JOIN branches b ON im.branch_id = b.id
        LEFT JOIN users u ON im.user_id = u.id
        $whereClause
        ORDER BY im.created_at DESC
        LIMIT @limit
      ''', parameters);

      return Response.ok(
        jsonEncode({'movements': movements}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to fetch inventory movements: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getLowStockItems(Request request) async {
    try {
      final branchId = request.url.queryParameters['branch_id'];
      
      String whereClause = '';
      Map<String, dynamic> parameters = {};

      if (branchId != null) {
        whereClause = 'AND bi.branch_id = @branch_id';
        parameters['branch_id'] = int.parse(branchId);
      }

      final lowStockItems = await DatabaseService.instance.query('''
        SELECT bi.*, p.name_en, p.name_ar, p.sku, p.min_stock_level,
               b.name_en as branch_name
        FROM branch_inventory bi
        JOIN products p ON bi.product_id = p.id
        JOIN branches b ON bi.branch_id = b.id
        WHERE p.is_active = true 
        AND bi.quantity <= p.min_stock_level
        $whereClause
        ORDER BY (bi.quantity::float / NULLIF(p.min_stock_level, 0)) ASC
      ''', parameters);

      return Response.ok(
        jsonEncode({'low_stock_items': lowStockItems}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to fetch low stock items: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}
