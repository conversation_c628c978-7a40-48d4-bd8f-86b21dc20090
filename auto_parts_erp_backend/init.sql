
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    email VARCHAR(255),
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    role VA<PERSON><PERSON><PERSON>(50) DEFAULT 'employee',
    branch_id INT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE,
    last_login_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE branches (
    id SERIAL PRIMARY KEY,
    name_en VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(255),
    manager_name VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMES<PERSON>MP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Categories table
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name_en VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    description_en TEXT,
    description_ar TEXT,
    parent_id INT REFERENCES categories(id),
    icon_name VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Brands table
CREATE TABLE brands (
    id SERIAL PRIMARY KEY,
    name_en VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    description_en TEXT,
    description_ar TEXT,
    logo_url TEXT,
    website TEXT,
    country_of_origin VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name_en VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    description_en TEXT,
    description_ar TEXT,
    sku VARCHAR(100) UNIQUE,
    barcode VARCHAR(50),
    category_id INT REFERENCES categories(id),
    brand_id INT REFERENCES brands(id),
    cost_price DECIMAL(10,2) DEFAULT 0.0,
    selling_price DECIMAL(10,2) DEFAULT 0.0,
    weight DECIMAL(10,3),
    unit VARCHAR(20) DEFAULT 'piece',
    min_stock_level INT DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    has_expiry_date BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE product_oem_numbers (
    id SERIAL PRIMARY KEY,
    product_id INT NOT NULL REFERENCES products(id),
    oem_number VARCHAR(255) NOT NULL
);

CREATE TABLE product_car_compatibility (
    id SERIAL PRIMARY KEY,
    product_id INT NOT NULL REFERENCES products(id),
    car_make VARCHAR(255) NOT NULL,
    car_model VARCHAR(255) NOT NULL,
    car_year INT NOT NULL
);

CREATE TABLE branch_inventory (
    id SERIAL PRIMARY KEY,
    product_id INT NOT NULL REFERENCES products(id),
    branch_id INT NOT NULL REFERENCES branches(id),
    quantity INT NOT NULL DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE sync_queue (
    id SERIAL PRIMARY KEY,
    operation_type VARCHAR(50) NOT NULL, -- e.g., 'create', 'update', 'delete'
    table_name VARCHAR(100) NOT NULL,
    record_id INT NOT NULL,
    payload JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Customers table
CREATE TABLE customers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    tax_number VARCHAR(50),
    customer_type VARCHAR(20) DEFAULT 'INDIVIDUAL',
    credit_limit DECIMAL(10,2) DEFAULT 0.0,
    current_balance DECIMAL(10,2) DEFAULT 0.0,
    is_vip BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Suppliers table
CREATE TABLE suppliers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    tax_number VARCHAR(50),
    website TEXT,
    current_balance DECIMAL(10,2) DEFAULT 0.0,
    payment_terms INT DEFAULT 30,
    rating DECIMAL(2,1) DEFAULT 0.0,
    is_active BOOLEAN DEFAULT true,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Sales table
CREATE TABLE sales (
    id SERIAL PRIMARY KEY,
    invoice_number VARCHAR(100) UNIQUE NOT NULL,
    customer_id INT REFERENCES customers(id),
    branch_id INT NOT NULL REFERENCES branches(id),
    user_id INT NOT NULL REFERENCES users(id),
    sale_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    subtotal DECIMAL(10,2) DEFAULT 0.0,
    tax_amount DECIMAL(10,2) DEFAULT 0.0,
    discount_amount DECIMAL(10,2) DEFAULT 0.0,
    total_amount DECIMAL(10,2) DEFAULT 0.0,
    paid_amount DECIMAL(10,2) DEFAULT 0.0,
    remaining_amount DECIMAL(10,2) DEFAULT 0.0,
    payment_method VARCHAR(50),
    status VARCHAR(20) DEFAULT 'COMPLETED',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Sales items table
CREATE TABLE sales_items (
    id SERIAL PRIMARY KEY,
    sale_id INT NOT NULL REFERENCES sales(id) ON DELETE CASCADE,
    product_id INT NOT NULL REFERENCES products(id),
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0.0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Purchases table
CREATE TABLE purchases (
    id SERIAL PRIMARY KEY,
    invoice_number VARCHAR(100) UNIQUE NOT NULL,
    supplier_id INT NOT NULL REFERENCES suppliers(id),
    branch_id INT NOT NULL REFERENCES branches(id),
    user_id INT NOT NULL REFERENCES users(id),
    purchase_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    delivery_date TIMESTAMP WITH TIME ZONE,
    subtotal DECIMAL(10,2) DEFAULT 0.0,
    tax_amount DECIMAL(10,2) DEFAULT 0.0,
    discount_amount DECIMAL(10,2) DEFAULT 0.0,
    total_amount DECIMAL(10,2) DEFAULT 0.0,
    paid_amount DECIMAL(10,2) DEFAULT 0.0,
    remaining_amount DECIMAL(10,2) DEFAULT 0.0,
    payment_method VARCHAR(50),
    status VARCHAR(20) DEFAULT 'PENDING',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Purchase items table
CREATE TABLE purchase_items (
    id SERIAL PRIMARY KEY,
    purchase_id INT NOT NULL REFERENCES purchases(id) ON DELETE CASCADE,
    product_id INT NOT NULL REFERENCES products(id),
    quantity INT NOT NULL,
    unit_cost DECIMAL(10,2) NOT NULL,
    total_cost DECIMAL(10,2) NOT NULL,
    expiry_date DATE,
    batch_number VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Inventory movements table
CREATE TABLE inventory_movements (
    id SERIAL PRIMARY KEY,
    product_id INT NOT NULL REFERENCES products(id),
    branch_id INT NOT NULL REFERENCES branches(id),
    movement_type VARCHAR(20) NOT NULL,
    quantity INT NOT NULL,
    quantity_before INT,
    quantity_after INT,
    unit_cost DECIMAL(10,2),
    reference_type VARCHAR(20),
    reference_id INT,
    notes TEXT,
    user_id INT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Product OEM Numbers table
CREATE TABLE product_oem_numbers (
    id SERIAL PRIMARY KEY,
    product_id INT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    oem_number VARCHAR(100) NOT NULL,
    manufacturer VARCHAR(100),
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Product Car Compatibility table
CREATE TABLE product_car_compatibility (
    id SERIAL PRIMARY KEY,
    product_id INT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    car_make VARCHAR(50) NOT NULL,
    car_model VARCHAR(50) NOT NULL,
    year_from INT NOT NULL,
    year_to INT,
    engine_type VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Branch Inventory table
CREATE TABLE branch_inventory (
    id SERIAL PRIMARY KEY,
    product_id INT NOT NULL REFERENCES products(id),
    branch_id INT NOT NULL REFERENCES branches(id),
    quantity INT DEFAULT 0,
    reserved_quantity INT DEFAULT 0,
    average_cost DECIMAL(10,2) DEFAULT 0.0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_stock_in TIMESTAMP WITH TIME ZONE,
    last_stock_out TIMESTAMP WITH TIME ZONE,
    UNIQUE(product_id, branch_id)
);

-- Sync Queue table
CREATE TABLE sync_queue (
    id SERIAL PRIMARY KEY,
    operation_type VARCHAR(20) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id INT NOT NULL,
    payload TEXT,
    synced BOOLEAN DEFAULT false,
    retry_count INT DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    synced_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE shared_inventory (
    id SERIAL PRIMARY KEY,
    product_id INT NOT NULL REFERENCES products(id),
    owner_branch_id INT NOT NULL REFERENCES branches(id),
    quantity INT NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data
INSERT INTO branches (name_en, name_ar, address) VALUES
('Main Branch', 'الفرع الرئيسي', 'Main Office Address');

INSERT INTO users (username, password_hash, role, branch_id) VALUES
('admin', 'admin', 'admin', 1),
('manager', 'manager', 'manager', 1),
('employee', 'employee', 'employee', 1);

-- Insert categories
INSERT INTO categories (name_en, name_ar) VALUES
('Engine Parts', 'قطع المحرك'),
('Brake System', 'نظام الفرامل'),
('Oils & Fluids', 'الزيوت والسوائل'),
('Tires', 'الإطارات'),
('Batteries', 'البطاريات'),
('Filters', 'الفلاتر');

-- Insert brands
INSERT INTO brands (name_en, name_ar) VALUES
('Toyota', 'تويوتا'),
('Honda', 'هوندا'),
('Nissan', 'نيسان'),
('Hyundai', 'هيونداي'),
('Kia', 'كيا');

-- Insert sample products
INSERT INTO products (name_en, name_ar, description_en, description_ar, category_id, brand_id, cost_price, selling_price, sku) VALUES
('Engine Oil 5W-30', 'زيت المحرك 5W-30', 'High quality synthetic engine oil', 'زيت محرك صناعي عالي الجودة', 3, 1, 25.00, 35.00, 'OIL-5W30-001'),
('Brake Pads Front', 'فحمات الفرامل الأمامية', 'Premium ceramic brake pads', 'فحمات فرامل سيراميك ممتازة', 2, 1, 45.00, 65.00, 'BP-FRONT-001'),
('Air Filter', 'فلتر الهواء', 'High efficiency air filter', 'فلتر هواء عالي الكفاءة', 6, 2, 15.00, 25.00, 'AF-001'),
('Battery 12V 70Ah', 'بطارية 12 فولت 70 أمبير', 'Maintenance-free car battery', 'بطارية سيارة خالية من الصيانة', 5, 3, 120.00, 180.00, 'BAT-12V-70');

-- Insert sample customers
INSERT INTO customers (name, phone, email, customer_type) VALUES
('Ahmed Al-Rashid', '+966501234567', '<EMAIL>', 'INDIVIDUAL'),
('Al-Rashid Auto Parts', '+966501234568', '<EMAIL>', 'COMPANY'),
('Fatima Motors', '+966501234569', '<EMAIL>', 'COMPANY');

-- Insert sample suppliers
INSERT INTO suppliers (name, contact_person, phone, email, city, country) VALUES
('Toyota Parts Supplier', 'John Smith', '+1234567890', '<EMAIL>', 'Detroit', 'USA'),
('Honda Parts International', 'Yuki Tanaka', '+81234567890', '<EMAIL>', 'Tokyo', 'Japan'),
('Universal Auto Parts', 'Mohammed Ali', '+971501234567', '<EMAIL>', 'Dubai', 'UAE');

-- Insert initial inventory
INSERT INTO branch_inventory (product_id, branch_id, quantity) VALUES
(1, 1, 50),
(2, 1, 30),
(3, 1, 25),
(4, 1, 15);
