import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/core/database/database_provider.dart';
import 'package:auto_parts_erp/core/database/local/database.dart';

// HR data providers
final hrStatsProvider = FutureProvider<HRStats>((ref) async {
  final database = ref.watch(databaseProvider);
  // Mock data for now - replace with actual database calls
  return HRStats(
    totalEmployees: 45,
    activeEmployees: 42,
    onLeave: 3,
    newHires: 5,
    pendingRequests: 8,
    averageAttendance: 95.5,
  );
});

final attendanceDataProvider = FutureProvider<List<AttendanceData>>((ref) async {
  // Mock data for now
  return [
    AttendanceData(day: 'Mon', percentage: 98.0),
    AttendanceData(day: 'Tue', percentage: 96.5),
    AttendanceData(day: 'Wed', percentage: 94.0),
    AttendanceData(day: 'Thu', percentage: 97.5),
    AttendanceData(day: 'Fri', percentage: 93.0),
    AttendanceData(day: 'Sat', percentage: 89.0),
  ];
});

final recentHRActivitiesProvider = FutureProvider<List<HRActivity>>((ref) async {
  // Mock data for now
  return [
    HRActivity(
      id: 1,
      type: 'LEAVE_REQUEST',
      description: 'Ahmed Ali requested 3 days leave',
      timestamp: DateTime.now().subtract(const Duration(hours: 1)),
      status: 'PENDING',
      employeeName: 'Ahmed Ali',
    ),
    HRActivity(
      id: 2,
      type: 'NEW_HIRE',
      description: 'Sara Mohamed joined as Sales Associate',
      timestamp: DateTime.now().subtract(const Duration(hours: 4)),
      status: 'COMPLETED',
      employeeName: 'Sara Mohamed',
    ),
    HRActivity(
      id: 3,
      type: 'PERFORMANCE_REVIEW',
      description: 'Monthly review for Omar Hassan',
      timestamp: DateTime.now().subtract(const Duration(hours: 6)),
      status: 'IN_PROGRESS',
      employeeName: 'Omar Hassan',
    ),
  ];
});

// Data models
class HRStats {
  final int totalEmployees;
  final int activeEmployees;
  final int onLeave;
  final int newHires;
  final int pendingRequests;
  final double averageAttendance;

  HRStats({
    required this.totalEmployees,
    required this.activeEmployees,
    required this.onLeave,
    required this.newHires,
    required this.pendingRequests,
    required this.averageAttendance,
  });
}

class AttendanceData {
  final String day;
  final double percentage;

  AttendanceData({
    required this.day,
    required this.percentage,
  });
}

class HRActivity {
  final int id;
  final String type;
  final String description;
  final DateTime timestamp;
  final String status;
  final String employeeName;

  HRActivity({
    required this.id,
    required this.type,
    required this.description,
    required this.timestamp,
    required this.status,
    required this.employeeName,
  });
}

class HRDashboardScreen extends ConsumerWidget {
  const HRDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hrStats = ref.watch(hrStatsProvider);
    final attendanceData = ref.watch(attendanceDataProvider);
    final activities = ref.watch(recentHRActivitiesProvider);

    return ScaffoldPage(
      header: PageHeader(
        title: Text(AppLocalizations.of(context).translate('hrDashboard')),
        commandBar: Row(
          children: [
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.add_friend),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('addEmployee')),
                ],
              ),
              onPressed: () {
                _showAddEmployeeDialog(context);
              },
            ),
            const SizedBox(width: 8),
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.calendar),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('attendance')),
                ],
              ),
              onPressed: () {
                // TODO: Navigate to attendance management
              },
            ),
            const SizedBox(width: 8),
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.money),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('payroll')),
                ],
              ),
              onPressed: () {
                // TODO: Navigate to payroll management
              },
            ),
          ],
        ),
      ),
      content: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Stats Cards Row
            hrStats.when(
              data: (stats) => Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      context,
                      AppLocalizations.of(context).translate('totalEmployees'),
                      stats.totalEmployees.toString(),
                      FluentIcons.people,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      AppLocalizations.of(context).translate('activeEmployees'),
                      stats.activeEmployees.toString(),
                      FluentIcons.contact,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      AppLocalizations.of(context).translate('onLeave'),
                      stats.onLeave.toString(),
                      FluentIcons.calendar_day,
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      AppLocalizations.of(context).translate('newHires'),
                      stats.newHires.toString(),
                      FluentIcons.add_friend,
                      Colors.purple,
                    ),
                  ),
                ],
              ),
              loading: () => const Center(child: ProgressRing()),
              error: (error, stack) => Center(
                child: Text('Error loading HR stats: $error'),
              ),
            ),
            const SizedBox(height: 24),

            // Attendance and Pending Requests Row
            Row(
              children: [
                // Average Attendance Card
                Expanded(
                  child: hrStats.when(
                    data: (stats) => Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context).translate('averageAttendance'),
                              style: FluentTheme.of(context).typography.subtitle,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: LinearProgressIndicator(
                                    value: stats.averageAttendance / 100.0,
                                    backgroundColor: Colors.grey.withValues(alpha: 0.3),
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      stats.averageAttendance >= 90.0 ? Colors.green : Colors.orange,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Text(
                                  '${stats.averageAttendance.toStringAsFixed(1)}%',
                                  style: FluentTheme.of(context).typography.body?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: stats.averageAttendance >= 90.0 ? Colors.green : Colors.orange,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    loading: () => const Center(child: ProgressRing()),
                    error: (error, stack) => const SizedBox(),
                  ),
                ),
                const SizedBox(width: 16),

                // Pending Requests Card
                Expanded(
                  child: hrStats.when(
                    data: (stats) => Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context).translate('pendingRequests'),
                              style: FluentTheme.of(context).typography.subtitle,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Icon(
                                  FluentIcons.clock,
                                  size: 24,
                                  color: stats.pendingRequests > 5 ? Colors.red : Colors.orange,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '${stats.pendingRequests} requests',
                                  style: FluentTheme.of(context).typography.body?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: stats.pendingRequests > 5 ? Colors.red : Colors.orange,
                                  ),
                                ),
                                const Spacer(),
                                Button(
                                  child: Text(AppLocalizations.of(context).translate('review')),
                                  onPressed: () {
                                    // TODO: Navigate to pending requests
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    loading: () => const Center(child: ProgressRing()),
                    error: (error, stack) => const SizedBox(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Attendance Chart and Recent Activities
            Row(
              children: [
                // Weekly Attendance Chart
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).translate('weeklyAttendance'),
                            style: FluentTheme.of(context).typography.subtitle,
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 300,
                            child: attendanceData.when(
                              data: (data) => BarChart(
                                BarChartData(
                                  alignment: BarChartAlignment.spaceAround,
                                  maxY: 100,
                                  barTouchData: BarTouchData(enabled: false),
                                  titlesData: FlTitlesData(
                                    show: true,
                                    bottomTitles: AxisTitles(
                                      sideTitles: SideTitles(
                                        showTitles: true,
                                        getTitlesWidget: (value, meta) {
                                          if (value.toInt() < data.length) {
                                            return Text(data[value.toInt()].day);
                                          }
                                          return const Text('');
                                        },
                                      ),
                                    ),
                                    leftTitles: const AxisTitles(
                                      sideTitles: SideTitles(showTitles: true),
                                    ),
                                    topTitles: const AxisTitles(
                                      sideTitles: SideTitles(showTitles: false),
                                    ),
                                    rightTitles: const AxisTitles(
                                      sideTitles: SideTitles(showTitles: false),
                                    ),
                                  ),
                                  borderData: FlBorderData(show: false),
                                  barGroups: data.asMap().entries.map((entry) {
                                    return BarChartGroupData(
                                      x: entry.key,
                                      barRods: [
                                        BarChartRodData(
                                          toY: entry.value.percentage,
                                          color: entry.value.percentage >= 95.0 
                                              ? Colors.green 
                                              : entry.value.percentage >= 90.0 
                                                  ? Colors.orange 
                                                  : Colors.red,
                                          width: 20,
                                        ),
                                      ],
                                    );
                                  }).toList(),
                                ),
                              ),
                              loading: () => const Center(child: ProgressRing()),
                              error: (error, stack) => Center(
                                child: Text('Error loading attendance data: $error'),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Recent HR Activities
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).translate('recentActivities'),
                            style: FluentTheme.of(context).typography.subtitle,
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 300,
                            child: activities.when(
                              data: (activitiesList) => ListView.builder(
                                itemCount: activitiesList.length,
                                itemBuilder: (context, index) {
                                  final activity = activitiesList[index];
                                  return _buildActivityItem(context, activity);
                                },
                              ),
                              loading: () => const Center(child: ProgressRing()),
                              error: (error, stack) => Center(
                                child: Text('Error loading activities: $error'),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(BuildContext context, String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 24, color: color),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: FluentTheme.of(context).typography.body,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: FluentTheme.of(context).typography.title?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(BuildContext context, HRActivity activity) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getActivityIcon(activity.type),
                size: 20,
                color: _getActivityColor(activity.status),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  activity.description,
                  style: FluentTheme.of(context).typography.body?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getActivityColor(activity.status).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _getActivityColor(activity.status).withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  activity.status,
                  style: TextStyle(
                    color: _getActivityColor(activity.status),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '${activity.employeeName} • ${_formatDateTime(activity.timestamp)}',
            style: FluentTheme.of(context).typography.caption,
          ),
        ],
      ),
    );
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'LEAVE_REQUEST':
        return FluentIcons.calendar_day;
      case 'NEW_HIRE':
        return FluentIcons.add_friend;
      case 'PERFORMANCE_REVIEW':
        return FluentIcons.chart_series;
      default:
        return FluentIcons.info;
    }
  }

  Color _getActivityColor(String status) {
    switch (status) {
      case 'COMPLETED':
        return Colors.green;
      case 'IN_PROGRESS':
        return Colors.blue;
      case 'PENDING':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showAddEmployeeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ContentDialog(
        title: Text(AppLocalizations.of(context).translate('addEmployee')),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Add employee form will be implemented here.'),
          ],
        ),
        actions: [
          Button(
            child: Text(AppLocalizations.of(context).translate('cancel')),
            onPressed: () => Navigator.of(context).pop(),
          ),
          FilledButton(
            child: Text(AppLocalizations.of(context).translate('save')),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}
