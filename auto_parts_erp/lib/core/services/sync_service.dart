import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../database/database_provider.dart';
import '../network/api_client.dart';
import '../constants/app_constants.dart';

// Sync service provider
final syncServiceProvider = Provider<SyncService>((ref) {
  final database = ref.watch(databaseProvider);
  final apiClient = ref.watch(apiClientProvider);
  return SyncService(database, apiClient);
});

// Sync status provider
final syncStatusProvider = StateNotifierProvider<SyncStatusNotifier, SyncStatus>((ref) {
  final syncService = ref.watch(syncServiceProvider);
  return SyncStatusNotifier(syncService);
});

class SyncService {
  final AppDatabase _database;
  final ApiClient _apiClient;
  
  SyncService(this._database, this._apiClient);
  
  // Push local changes to server
  Future<SyncResult> pushToServer() async {
    try {
      // Get pending sync items
      final pendingItems = await _database.getPendingSyncItems();
      
      if (pendingItems.isEmpty) {
        return SyncResult.success(message: 'No changes to sync');
      }
      
      // Prepare operations for API
      final operations = pendingItems.map((item) => {
        'id': item.id,
        'operation_type': item.operationType,
        'table_name': item.tableName,
        'record_id': item.recordId,
        'data': item.payload != null ? jsonDecode(item.payload!) : null,
      }).toList();
      
      // Send to server
      final response = await _apiClient.syncData(operations);
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.data);
        final results = responseData['results'] as List<dynamic>;
        
        // Mark successful operations as synced
        for (final result in results) {
          if (result['status'] == 'success') {
            final operationId = result['operation_id'];
            await _database.markAsSynced(operationId);
          }
        }
        
        return SyncResult.success(
          message: 'Pushed ${results.where((r) => r['status'] == 'success').length} changes'
        );
      } else {
        return SyncResult.error('Failed to push changes: ${response.statusCode}');
      }
    } catch (e) {
      return SyncResult.error('Push failed: $e');
    }
  }
  
  // Pull changes from server
  Future<SyncResult> pullFromServer() async {
    try {
      // Get last sync time
      final lastSyncTime = await _getLastSyncTime();
      
      // Pull updates from server
      final response = await _apiClient.pullUpdates(lastSyncTime);
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.data);
        final updates = responseData['updates'] as Map<String, dynamic>;
        
        int totalUpdates = 0;
        
        // Process each type of update
        for (final entry in updates.entries) {
          final tableName = entry.key;
          final records = entry.value as List<dynamic>;
          
          totalUpdates += await _processTableUpdates(tableName, records);
        }
        
        // Update last sync time
        await _updateLastSyncTime(DateTime.parse(responseData['last_sync']));
        
        return SyncResult.success(message: 'Pulled $totalUpdates updates');
      } else {
        return SyncResult.error('Failed to pull updates: ${response.statusCode}');
      }
    } catch (e) {
      return SyncResult.error('Pull failed: $e');
    }
  }
  
  // Full sync (push then pull)
  Future<SyncResult> fullSync() async {
    try {
      // First push local changes
      final pushResult = await pushToServer();
      if (!pushResult.success) {
        return pushResult;
      }
      
      // Then pull server updates
      final pullResult = await pullFromServer();
      if (!pullResult.success) {
        return pullResult;
      }
      
      return SyncResult.success(
        message: 'Full sync completed. ${pushResult.message}, ${pullResult.message}'
      );
    } catch (e) {
      return SyncResult.error('Full sync failed: $e');
    }
  }
  
  // Add operation to sync queue
  Future<void> queueOperation(String operationType, String tableName, int recordId, Map<String, dynamic>? data) async {
    await _database.into(_database.syncQueue).insert(
      SyncQueueCompanion.insert(
        operationType: operationType,
        tableName: tableName,
        recordId: recordId,
        payload: Value(data != null ? jsonEncode(data) : null),
      ),
    );
  }
  
  // Process table updates from server
  Future<int> _processTableUpdates(String tableName, List<dynamic> records) async {
    int count = 0;
    
    for (final record in records) {
      try {
        switch (tableName) {
          case 'products':
            await _updateProduct(record);
            break;
          case 'categories':
            await _updateCategory(record);
            break;
          case 'brands':
            await _updateBrand(record);
            break;
          case 'inventory':
            await _updateInventory(record);
            break;
          case 'sales':
            await _updateSale(record);
            break;
          case 'purchases':
            await _updatePurchase(record);
            break;
          case 'customers':
            await _updateCustomer(record);
            break;
          case 'suppliers':
            await _updateSupplier(record);
            break;
        }
        count++;
      } catch (e) {
        print('Failed to update $tableName record ${record['id']}: $e');
      }
    }
    
    return count;
  }
  
  // Update methods for each table
  Future<void> _updateProduct(Map<String, dynamic> data) async {
    await _database.into(_database.products).insertOnConflictUpdate(
      ProductsCompanion(
        id: Value(data['id']),
        nameEn: Value(data['name_en']),
        nameAr: Value(data['name_ar']),
        descriptionEn: Value(data['description_en']),
        descriptionAr: Value(data['description_ar']),
        sku: Value(data['sku']),
        barcode: Value(data['barcode']),
        categoryId: Value(data['category_id']),
        brandId: Value(data['brand_id']),
        costPrice: Value(data['cost_price']?.toDouble() ?? 0.0),
        sellingPrice: Value(data['selling_price']?.toDouble() ?? 0.0),
        weight: Value(data['weight']?.toDouble()),
        unit: Value(data['unit'] ?? 'piece'),
        minStockLevel: Value(data['min_stock_level'] ?? 0),
        isActive: Value(data['is_active'] ?? true),
        hasExpiryDate: Value(data['has_expiry_date'] ?? false),
        createdAt: Value(DateTime.parse(data['created_at'])),
        updatedAt: Value(data['updated_at'] != null ? DateTime.parse(data['updated_at']) : null),
      ),
    );
  }
  
  Future<void> _updateCategory(Map<String, dynamic> data) async {
    await _database.into(_database.categories).insertOnConflictUpdate(
      CategoriesCompanion(
        id: Value(data['id']),
        nameEn: Value(data['name_en']),
        nameAr: Value(data['name_ar']),
        descriptionEn: Value(data['description_en']),
        descriptionAr: Value(data['description_ar']),
        parentId: Value(data['parent_id']),
        iconName: Value(data['icon_name']),
        isActive: Value(data['is_active'] ?? true),
        createdAt: Value(DateTime.parse(data['created_at'])),
        updatedAt: Value(data['updated_at'] != null ? DateTime.parse(data['updated_at']) : null),
      ),
    );
  }
  
  Future<void> _updateBrand(Map<String, dynamic> data) async {
    await _database.into(_database.brands).insertOnConflictUpdate(
      BrandsCompanion(
        id: Value(data['id']),
        nameEn: Value(data['name_en']),
        nameAr: Value(data['name_ar']),
        descriptionEn: Value(data['description_en']),
        descriptionAr: Value(data['description_ar']),
        logoUrl: Value(data['logo_url']),
        website: Value(data['website']),
        countryOfOrigin: Value(data['country_of_origin']),
        isActive: Value(data['is_active'] ?? true),
        createdAt: Value(DateTime.parse(data['created_at'])),
        updatedAt: Value(data['updated_at'] != null ? DateTime.parse(data['updated_at']) : null),
      ),
    );
  }
  
  Future<void> _updateInventory(Map<String, dynamic> data) async {
    await _database.into(_database.branchInventory).insertOnConflictUpdate(
      BranchInventoryCompanion(
        id: Value(data['id']),
        productId: Value(data['product_id']),
        branchId: Value(data['branch_id']),
        quantity: Value(data['quantity'] ?? 0),
        reservedQuantity: Value(data['reserved_quantity'] ?? 0),
        averageCost: Value(data['average_cost']?.toDouble() ?? 0.0),
        lastUpdated: Value(DateTime.parse(data['last_updated'])),
        lastStockIn: Value(data['last_stock_in'] != null ? DateTime.parse(data['last_stock_in']) : null),
        lastStockOut: Value(data['last_stock_out'] != null ? DateTime.parse(data['last_stock_out']) : null),
      ),
    );
  }
  
  Future<void> _updateSale(Map<String, dynamic> data) async {
    // Update sale record
    await _database.into(_database.sales).insertOnConflictUpdate(
      SalesCompanion(
        id: Value(data['id']),
        invoiceNumber: Value(data['invoice_number']),
        customerId: Value(data['customer_id']),
        branchId: Value(data['branch_id']),
        userId: Value(data['user_id']),
        saleDate: Value(DateTime.parse(data['sale_date'])),
        subtotal: Value(data['subtotal']?.toDouble() ?? 0.0),
        taxAmount: Value(data['tax_amount']?.toDouble() ?? 0.0),
        discountAmount: Value(data['discount_amount']?.toDouble() ?? 0.0),
        totalAmount: Value(data['total_amount']?.toDouble() ?? 0.0),
        paidAmount: Value(data['paid_amount']?.toDouble() ?? 0.0),
        remainingAmount: Value(data['remaining_amount']?.toDouble() ?? 0.0),
        paymentMethod: Value(data['payment_method']),
        status: Value(data['status'] ?? 'COMPLETED'),
        notes: Value(data['notes']),
        createdAt: Value(DateTime.parse(data['created_at'])),
        updatedAt: Value(data['updated_at'] != null ? DateTime.parse(data['updated_at']) : null),
      ),
    );
    
    // Update sale items if provided
    if (data['items'] != null) {
      final items = data['items'] as List<dynamic>;
      for (final item in items) {
        await _database.into(_database.salesItems).insertOnConflictUpdate(
          SalesItemsCompanion(
            id: Value(item['id']),
            saleId: Value(data['id']),
            productId: Value(item['product_id']),
            quantity: Value(item['quantity']),
            unitPrice: Value(item['unit_price']?.toDouble() ?? 0.0),
            totalPrice: Value(item['total_price']?.toDouble() ?? 0.0),
            discountAmount: Value(item['discount_amount']?.toDouble() ?? 0.0),
            notes: Value(item['notes']),
            createdAt: Value(DateTime.parse(item['created_at'])),
          ),
        );
      }
    }
  }
  
  Future<void> _updatePurchase(Map<String, dynamic> data) async {
    // Similar to _updateSale but for purchases
    // Implementation would be similar to sales
  }
  
  Future<void> _updateCustomer(Map<String, dynamic> data) async {
    await _database.into(_database.customers).insertOnConflictUpdate(
      CustomersCompanion(
        id: Value(data['id']),
        name: Value(data['name']),
        email: Value(data['email']),
        phone: Value(data['phone']),
        address: Value(data['address']),
        city: Value(data['city']),
        taxNumber: Value(data['tax_number']),
        customerType: Value(data['customer_type'] ?? 'INDIVIDUAL'),
        creditLimit: Value(data['credit_limit']?.toDouble() ?? 0.0),
        currentBalance: Value(data['current_balance']?.toDouble() ?? 0.0),
        isVip: Value(data['is_vip'] ?? false),
        isActive: Value(data['is_active'] ?? true),
        notes: Value(data['notes']),
        createdAt: Value(DateTime.parse(data['created_at'])),
        updatedAt: Value(data['updated_at'] != null ? DateTime.parse(data['updated_at']) : null),
      ),
    );
  }
  
  Future<void> _updateSupplier(Map<String, dynamic> data) async {
    await _database.into(_database.suppliers).insertOnConflictUpdate(
      SuppliersCompanion(
        id: Value(data['id']),
        name: Value(data['name']),
        contactPerson: Value(data['contact_person']),
        email: Value(data['email']),
        phone: Value(data['phone']),
        address: Value(data['address']),
        city: Value(data['city']),
        country: Value(data['country']),
        taxNumber: Value(data['tax_number']),
        website: Value(data['website']),
        currentBalance: Value(data['current_balance']?.toDouble() ?? 0.0),
        paymentTerms: Value(data['payment_terms'] ?? 30),
        rating: Value(data['rating']?.toDouble() ?? 0.0),
        isActive: Value(data['is_active'] ?? true),
        notes: Value(data['notes']),
        createdAt: Value(DateTime.parse(data['created_at'])),
        updatedAt: Value(data['updated_at'] != null ? DateTime.parse(data['updated_at']) : null),
      ),
    );
  }
  
  Future<DateTime> _getLastSyncTime() async {
    // Get from shared preferences or database
    // For now, return a default time
    return DateTime.now().subtract(Duration(days: 30));
  }
  
  Future<void> _updateLastSyncTime(DateTime time) async {
    // Save to shared preferences
    // Implementation depends on your preference storage
  }
}

// Sync status classes
class SyncStatus {
  final bool isLoading;
  final String? message;
  final DateTime? lastSync;
  final int pendingOperations;
  
  const SyncStatus({
    this.isLoading = false,
    this.message,
    this.lastSync,
    this.pendingOperations = 0,
  });
  
  SyncStatus copyWith({
    bool? isLoading,
    String? message,
    DateTime? lastSync,
    int? pendingOperations,
  }) {
    return SyncStatus(
      isLoading: isLoading ?? this.isLoading,
      message: message ?? this.message,
      lastSync: lastSync ?? this.lastSync,
      pendingOperations: pendingOperations ?? this.pendingOperations,
    );
  }
}

class SyncStatusNotifier extends StateNotifier<SyncStatus> {
  final SyncService _syncService;
  
  SyncStatusNotifier(this._syncService) : super(const SyncStatus());
  
  Future<void> sync() async {
    state = state.copyWith(isLoading: true, message: 'Syncing...');
    
    final result = await _syncService.fullSync();
    
    state = state.copyWith(
      isLoading: false,
      message: result.message,
      lastSync: result.success ? DateTime.now() : state.lastSync,
    );
  }
  
  Future<void> pushOnly() async {
    state = state.copyWith(isLoading: true, message: 'Pushing changes...');
    
    final result = await _syncService.pushToServer();
    
    state = state.copyWith(
      isLoading: false,
      message: result.message,
    );
  }
  
  Future<void> pullOnly() async {
    state = state.copyWith(isLoading: true, message: 'Pulling updates...');
    
    final result = await _syncService.pullFromServer();
    
    state = state.copyWith(
      isLoading: false,
      message: result.message,
      lastSync: result.success ? DateTime.now() : state.lastSync,
    );
  }
}

class SyncResult {
  final bool success;
  final String message;
  
  const SyncResult.success({required this.message}) : success = true;
  const SyncResult.error(this.message) : success = false;
}
