import 'package:auto_parts_erp/features/purchases/data/models/purchase_order_model.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class PurchaseOrdersScreen extends StatefulWidget {
  const PurchaseOrdersScreen({super.key});

  @override
  State<PurchaseOrdersScreen> createState() => _PurchaseOrdersScreenState();
}

class _PurchaseOrdersScreenState extends State<PurchaseOrdersScreen> {
  List<PurchaseOrder> purchaseOrders = <PurchaseOrder>[];
  late PurchaseOrderDataSource purchaseOrderDataSource;

  @override
  void initState() {
    super.initState();
    populateData();
  }

  void populateData() {
    purchaseOrders = <PurchaseOrder>[
      PurchaseOrder(id: '1', poNumber: 'PO-2024-001', supplier: 'Supplier X', orderDate: '2024-06-25', status: 'Pending', totalAmount: 5000.00),
      PurchaseOrder(id: '2', poNumber: 'PO-2024-002', supplier: 'Supplier Y', orderDate: '2024-06-20', status: 'Completed', totalAmount: 7500.00),
    ];
    purchaseOrderDataSource = PurchaseOrderDataSource(purchaseOrders: purchaseOrders);
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldPage(
      header: const PageHeader(title: Text('Purchases')),
      content: Column(
        children: [
          Row(
            children: [
              const Expanded(child: TextBox(placeholder: 'Search Purchase Orders')),
              const SizedBox(width: 10),
              FilledButton(child: const Text('Create Purchase Order'), onPressed: () {}),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SfDataGrid(
              source: purchaseOrderDataSource,
              columnWidthMode: ColumnWidthMode.fill,
              columns: <GridColumn>[
                GridColumn(columnName: 'poNumber', label: const Text('PO Number')),
                GridColumn(columnName: 'supplier', label: const Text('Supplier')),
                GridColumn(columnName: 'orderDate', label: const Text('Order Date')),
                GridColumn(columnName: 'status', label: const Text('Status')),
                GridColumn(columnName: 'totalAmount', label: const Text('Total Amount')),
                GridColumn(
                  columnName: 'actions',
                  label: const Text('Actions'),
                  columnWidthMode: ColumnWidthMode.auto,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class PurchaseOrderDataSource extends DataGridSource {
  PurchaseOrderDataSource({required List<PurchaseOrder> purchaseOrders}) {
    _purchaseOrders = purchaseOrders
        .map<DataGridRow>((
          e) => DataGridRow(cells: [
              DataGridCell<String>(columnName: 'poNumber', value: e.poNumber),
              DataGridCell<String>(columnName: 'supplier', value: e.supplier),
              DataGridCell<String>(columnName: 'orderDate', value: e.orderDate),
              DataGridCell<String>(columnName: 'status', value: e.status),
              DataGridCell<double>(columnName: 'totalAmount', value: e.totalAmount),
              DataGridCell<Widget>(columnName: 'actions', value: Row(
                children: [
                  IconButton(icon: const Icon(FluentIcons.edit), onPressed: () {}),
                  IconButton(icon: const Icon(FluentIcons.receipt_processing), onPressed: () {}),
                ],
              )),
            ]))
        .toList();
  }

  List<DataGridRow> _purchaseOrders = [];

  @override
  List<DataGridRow> get rows => _purchaseOrders;

  @override
  DataGridRowAdapter? buildRow(DataGridRow row) {
    return DataGridRowAdapter(
        cells: row.getCells().map<Widget>((
      e) {
      return Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.all(8.0),
        child: e.value is Widget ? e.value : Text(e.value.toString()),
      );
    }).toList());
  }
}