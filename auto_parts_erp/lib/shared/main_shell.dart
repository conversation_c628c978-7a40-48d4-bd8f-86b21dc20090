import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/localization/localization_service.dart';
import '../core/theme/app_theme.dart';

class MainShell extends ConsumerStatefulWidget {
  final Widget child;
  
  const MainShell({super.key, required this.child});

  @override
  ConsumerState<MainShell> createState() => _MainShellState();
}

class _MainShellState extends ConsumerState<MainShell> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final locale = ref.watch(currentLocaleProvider);
    final themeMode = ref.watch(themeModeProvider);
    
    return NavigationView(
      appBar: NavigationAppBar(
        title: Text(AppLocalizations.of(context).translate('appTitle')),
        actions: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // Language toggle
            ToggleButton(
              checked: locale.languageCode == 'ar',
              onChanged: (value) {
                ref.read(currentLocaleProvider.notifier).state = 
                    value ? const Locale('ar') : const Locale('en');
              },
              child: Text(locale.languageCode == 'ar' ? 'عربي' : 'EN'),
            ),
            const SizedBox(width: 8),
            
            // Theme toggle
            ToggleButton(
              checked: themeMode == ThemeMode.dark,
              onChanged: (value) {
                ref.read(themeModeProvider.notifier).state = 
                    value ? ThemeMode.dark : ThemeMode.light;
              },
              child: Icon(themeMode == ThemeMode.dark 
                  ? FluentIcons.sunny : FluentIcons.clear_night),
            ),
            const SizedBox(width: 8),
            
            // User menu
            DropDownButton(
              leading: const Icon(FluentIcons.contact),
              title: const Text('Admin'),
              items: [
                MenuFlyoutItem(
                  text: Text(AppLocalizations.of(context).translate('settings')),
                  onPressed: () {
                    // Navigate to settings
                  },
                ),
                MenuFlyoutSeparator(),
                MenuFlyoutItem(
                  text: Text(AppLocalizations.of(context).translate('logout')),
                  onPressed: () {
                    // Handle logout
                  },
                ),
              ],
            ),
          ],
        ),
      ),
      pane: NavigationPane(
        selected: _currentIndex,
        onChanged: (index) => setState(() => _currentIndex = index),
        displayMode: PaneDisplayMode.auto,
        items: [
          // Dashboard
          PaneItem(
            icon: const Icon(FluentIcons.view_dashboard),
            title: Text(AppLocalizations.of(context).translate('dashboard')),
            body: widget.child,
          ),
          
          // Products
          PaneItemExpander(
            icon: const Icon(FluentIcons.product_variant),
            title: Text(AppLocalizations.of(context).translate('products')),
            body: widget.child,
            items: [
              PaneItem(
                icon: const Icon(FluentIcons.add),
                title: Text(AppLocalizations.of(context).translate('addProduct')),
                body: widget.child,
              ),
              PaneItem(
                icon: const Icon(FluentIcons.list),
                title: Text(AppLocalizations.of(context).translate('productList')),
                body: widget.child,
              ),
            ],
          ),
          
          // Inventory
          PaneItem(
            icon: const Icon(FluentIcons.package),
            title: Text(AppLocalizations.of(context).translate('inventory')),
            body: widget.child,
          ),
          
          // Sales
          PaneItemExpander(
            icon: const Icon(FluentIcons.shopping_cart),
            title: Text(AppLocalizations.of(context).translate('sales')),
            body: widget.child,
            items: [
              PaneItem(
                icon: const Icon(FluentIcons.point_of_sale),
                title: const Text('POS'),
                body: widget.child,
              ),
            ],
          ),
          
          // Purchases
          PaneItem(
            icon: const Icon(FluentIcons.shop),
            title: Text(AppLocalizations.of(context).translate('purchases')),
            body: widget.child,
          ),
          
          // Reports
          PaneItem(
            icon: const Icon(FluentIcons.chart),
            title: Text(AppLocalizations.of(context).translate('reports')),
            body: widget.child,
          ),
        ],
        footerItems: [
          PaneItem(
            icon: const Icon(FluentIcons.settings),
            title: Text(AppLocalizations.of(context).translate('settings')),
            body: widget.child,
          ),
        ],
      ),
    );
  }
}
