import 'package:drift/drift.dart';

@DataClassName('BranchInventoryData')
class BranchInventory extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get productId => integer()();
  IntColumn get branchId => integer()();
  IntColumn get quantity => integer().withDefault(const Constant(0))();
  IntColumn get reservedQuantity => integer().withDefault(const Constant(0))();
  RealColumn get averageCost => real().withDefault(const Constant(0.0))();
  DateTimeColumn get lastUpdated => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get lastStockIn => dateTime().nullable()();
  DateTimeColumn get lastStockOut => dateTime().nullable()();
}

@DataClassName('InventoryMovement')
class InventoryMovements extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get productId => integer()();
  IntColumn get branchId => integer()();
  TextColumn get movementType => text()(); // IN, OUT, TRANSFER, ADJUSTMENT
  IntColumn get quantity => integer()();
  IntColumn get quantityBefore => integer()();
  IntColumn get quantityAfter => integer()();
  RealColumn get unitCost => real().nullable()();
  TextColumn get referenceType => text().nullable()(); // SALE, PURCHASE, TRANSFER, ADJUSTMENT
  IntColumn get referenceId => integer().nullable()();
  TextColumn get notes => text().nullable()();
  IntColumn get userId => integer().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
}
