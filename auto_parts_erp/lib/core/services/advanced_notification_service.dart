import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../database/database_provider.dart';
import '../constants/app_constants.dart';

// Advanced notification service provider
final advancedNotificationServiceProvider = Provider<AdvancedNotificationService>((ref) {
  final database = ref.watch(databaseProvider);
  return AdvancedNotificationService(database);
});

// Notifications stream provider
final notificationsStreamProvider = StreamProvider<List<AppNotification>>((ref) {
  final service = ref.watch(advancedNotificationServiceProvider);
  return service.notificationsStream;
});

// Unread notifications count provider
final unreadNotificationsCountProvider = StreamProvider<int>((ref) {
  final service = ref.watch(advancedNotificationServiceProvider);
  return service.unreadCountStream;
});

class AdvancedNotificationService {
  final AppDatabase _database;
  final StreamController<List<AppNotification>> _notificationsController = StreamController.broadcast();
  final StreamController<int> _unreadCountController = StreamController.broadcast();
  final List<AppNotification> _notifications = [];
  late Timer _checkTimer;

  AdvancedNotificationService(this._database) {
    _initializeService();
  }

  Stream<List<AppNotification>> get notificationsStream => _notificationsController.stream;
  Stream<int> get unreadCountStream => _unreadCountController.stream;

  Future<void> _initializeService() async {
    // Load existing notifications
    await _loadNotifications();
    
    // Start periodic checks
    _startPeriodicChecks();
    
    // Initialize notification channels
    await _initializeNotificationChannels();
  }

  Future<void> _loadNotifications() async {
    final notifications = await _database.getRecentNotifications(limit: 100);
    _notifications.clear();
    _notifications.addAll(notifications);
    _notificationsController.add(List.from(_notifications));
    _updateUnreadCount();
  }

  void _startPeriodicChecks() {
    // Check for new notifications every minute
    _checkTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkForNewNotifications();
    });
  }

  Future<void> _initializeNotificationChannels() async {
    // Initialize different notification channels
    await _createNotificationChannel(
      channelId: 'inventory_alerts',
      channelName: 'Inventory Alerts',
      description: 'Low stock and inventory alerts',
      importance: NotificationImportance.high,
    );
    
    await _createNotificationChannel(
      channelId: 'sales_notifications',
      channelName: 'Sales Notifications',
      description: 'New orders and sales updates',
      importance: NotificationImportance.medium,
    );
    
    await _createNotificationChannel(
      channelId: 'system_alerts',
      channelName: 'System Alerts',
      description: 'System maintenance and security alerts',
      importance: NotificationImportance.critical,
    );
    
    await _createNotificationChannel(
      channelId: 'reminders',
      channelName: 'Reminders',
      description: 'Task and appointment reminders',
      importance: NotificationImportance.medium,
    );
  }

  Future<void> _createNotificationChannel({
    required String channelId,
    required String channelName,
    required String description,
    required NotificationImportance importance,
  }) async {
    // Store channel configuration in database
    await _database.createNotificationChannel(
      channelId: channelId,
      channelName: channelName,
      description: description,
      importance: importance.toString(),
    );
  }

  Future<void> _checkForNewNotifications() async {
    try {
      // Check for low stock alerts
      await _checkLowStockAlerts();
      
      // Check for pending orders
      await _checkPendingOrders();
      
      // Check for overdue payments
      await _checkOverduePayments();
      
      // Check for system maintenance
      await _checkSystemMaintenance();
      
      // Check for scheduled reminders
      await _checkScheduledReminders();
      
    } catch (e) {
      // TODO: Implement proper logging
    }
  }

  Future<void> _checkLowStockAlerts() async {
    // Mock implementation - replace with actual database calls
    final lowStockProducts = <LowStockProduct>[];
    
    for (final product in lowStockProducts) {
      // Check if we already sent an alert for this product recently
      final recentAlert = await _database.getRecentNotificationForProduct(
        productId: product.id,
        type: NotificationType.lowStock,
        since: DateTime.now().subtract(const Duration(hours: 24)),
      );
      
      if (recentAlert == null) {
        await _createNotification(
          type: NotificationType.lowStock,
          title: 'Low Stock Alert',
          message: 'Product "${product.name}" is running low (${product.currentStock} remaining)',
          channelId: 'inventory_alerts',
          priority: NotificationPriority.high,
          data: {'product_id': product.id, 'current_stock': product.currentStock},
          actionButtons: [
            NotificationAction(
              id: 'reorder',
              title: 'Reorder',
              action: 'navigate_to_reorder',
            ),
            NotificationAction(
              id: 'dismiss',
              title: 'Dismiss',
              action: 'dismiss_notification',
            ),
          ],
        );
      }
    }
  }

  Future<void> _checkPendingOrders() async {
    // Mock implementation - replace with actual database calls
    final pendingOrders = <PendingOrder>[];
    
    for (final order in pendingOrders) {
      // Check if order is overdue
      final daysSincePlaced = DateTime.now().difference(order.createdAt).inDays;
      
      if (daysSincePlaced > 3) {
        await _createNotification(
          type: NotificationType.orderOverdue,
          title: 'Order Overdue',
          message: 'Order #${order.orderNumber} has been pending for $daysSincePlaced days',
          channelId: 'sales_notifications',
          priority: NotificationPriority.medium,
          data: {'order_id': order.id, 'days_overdue': daysSincePlaced},
          actionButtons: [
            NotificationAction(
              id: 'view_order',
              title: 'View Order',
              action: 'navigate_to_order',
            ),
          ],
        );
      }
    }
  }

  Future<void> _checkOverduePayments() async {
    // Mock implementation - replace with actual database calls
    final overduePayments = <OverduePayment>[];
    
    for (final payment in overduePayments) {
      await _createNotification(
        type: NotificationType.paymentOverdue,
        title: 'Payment Overdue',
        message: 'Payment of \$${payment.amount} from ${payment.customerName} is overdue',
        channelId: 'sales_notifications',
        priority: NotificationPriority.high,
        data: {'payment_id': payment.id, 'amount': payment.amount},
        actionButtons: [
          NotificationAction(
            id: 'send_reminder',
            title: 'Send Reminder',
            action: 'send_payment_reminder',
          ),
        ],
      );
    }
  }

  Future<void> _checkSystemMaintenance() async {
    // Mock implementation - replace with actual database calls
    final maintenanceSchedule = <MaintenanceSchedule>[];
    
    for (final maintenance in maintenanceSchedule) {
      final hoursUntil = maintenance.scheduledTime.difference(DateTime.now()).inHours;
      
      if (hoursUntil <= 24 && hoursUntil > 0) {
        await _createNotification(
          type: NotificationType.systemMaintenance,
          title: 'Scheduled Maintenance',
          message: 'System maintenance scheduled in $hoursUntil hours',
          channelId: 'system_alerts',
          priority: NotificationPriority.critical,
          data: {'maintenance_id': maintenance.id, 'hours_until': hoursUntil},
        );
      }
    }
  }

  Future<void> _checkScheduledReminders() async {
    // Mock implementation - replace with actual database calls
    final reminders = <ReminderItem>[];
    
    for (final reminder in reminders) {
      await _createNotification(
        type: NotificationType.reminder,
        title: reminder.title,
        message: reminder.description,
        channelId: 'reminders',
        priority: NotificationPriority.medium,
        data: {'reminder_id': reminder.id},
        actionButtons: [
          NotificationAction(
            id: 'mark_done',
            title: 'Mark Done',
            action: 'mark_reminder_done',
          ),
          NotificationAction(
            id: 'snooze',
            title: 'Snooze',
            action: 'snooze_reminder',
          ),
        ],
      );
    }
  }

  Future<void> _createNotification({
    required NotificationType type,
    required String title,
    required String message,
    required String channelId,
    required NotificationPriority priority,
    Map<String, dynamic>? data,
    List<NotificationAction>? actionButtons,
    DateTime? scheduledTime,
  }) async {
    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: type,
      title: title,
      message: message,
      channelId: channelId,
      priority: priority,
      data: data ?? {},
      actionButtons: actionButtons ?? [],
      createdAt: DateTime.now(),
      scheduledTime: scheduledTime,
      isRead: false,
    );

    // Save to database
    await _database.saveNotification(notification);
    
    // Add to local list
    _notifications.insert(0, notification);
    
    // Update streams
    _notificationsController.add(List.from(_notifications));
    _updateUnreadCount();
    
    // Show system notification if app is in background
    await _showSystemNotification(notification);
  }

  Future<void> _showSystemNotification(AppNotification notification) async {
    // Implementation would depend on the platform
    // For now, just log the notification
    // TODO: Implement platform-specific notification display
  }

  Future<void> markAsRead(String notificationId) async {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      await _database.markNotificationAsRead(notificationId);
      _notificationsController.add(List.from(_notifications));
      _updateUnreadCount();
    }
  }

  Future<void> markAllAsRead() async {
    for (int i = 0; i < _notifications.length; i++) {
      if (!_notifications[i].isRead) {
        _notifications[i] = _notifications[i].copyWith(isRead: true);
      }
    }
    
    await _database.markAllNotificationsAsRead();
    _notificationsController.add(List.from(_notifications));
    _updateUnreadCount();
  }

  Future<void> deleteNotification(String notificationId) async {
    _notifications.removeWhere((n) => n.id == notificationId);
    await _database.deleteNotification(notificationId);
    _notificationsController.add(List.from(_notifications));
    _updateUnreadCount();
  }

  Future<void> handleNotificationAction(String notificationId, String actionId) async {
    final notification = _notifications.firstWhere(
      (n) => n.id == notificationId,
      orElse: () => AppNotification.empty(),
    );
    
    if (notification.id.isEmpty) return;
    
    final action = notification.actionButtons.firstWhere(
      (a) => a.id == actionId,
      orElse: () => NotificationAction.empty(),
    );
    
    if (action.id.isEmpty) return;
    
    switch (action.action) {
      case 'navigate_to_reorder':
        // Handle navigation to reorder screen
        break;
      case 'dismiss_notification':
        await deleteNotification(notificationId);
        break;
      case 'navigate_to_order':
        // Handle navigation to order screen
        break;
      case 'send_payment_reminder':
        // Handle sending payment reminder
        break;
      case 'mark_reminder_done':
        // Handle marking reminder as done
        break;
      case 'snooze_reminder':
        // Handle snoozing reminder
        break;
    }
  }

  void _updateUnreadCount() {
    final unreadCount = _notifications.where((n) => !n.isRead).length;
    _unreadCountController.add(unreadCount);
  }

  Future<void> scheduleNotification({
    required NotificationType type,
    required String title,
    required String message,
    required DateTime scheduledTime,
    String? channelId,
    NotificationPriority? priority,
    Map<String, dynamic>? data,
  }) async {
    await _createNotification(
      type: type,
      title: title,
      message: message,
      channelId: channelId ?? 'reminders',
      priority: priority ?? NotificationPriority.medium,
      data: data,
      scheduledTime: scheduledTime,
    );
  }

  void dispose() {
    _checkTimer.cancel();
    _notificationsController.close();
    _unreadCountController.close();
  }
}

// Data models
enum NotificationType {
  lowStock,
  orderOverdue,
  paymentOverdue,
  systemMaintenance,
  reminder,
  securityAlert,
  newMessage,
}

enum NotificationPriority {
  low,
  medium,
  high,
  critical,
}

enum NotificationImportance {
  low,
  medium,
  high,
  critical,
}

class AppNotification {
  final String id;
  final NotificationType type;
  final String title;
  final String message;
  final String channelId;
  final NotificationPriority priority;
  final Map<String, dynamic> data;
  final List<NotificationAction> actionButtons;
  final DateTime createdAt;
  final DateTime? scheduledTime;
  final bool isRead;

  AppNotification({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    required this.channelId,
    required this.priority,
    required this.data,
    required this.actionButtons,
    required this.createdAt,
    this.scheduledTime,
    required this.isRead,
  });

  AppNotification.empty()
      : id = '',
        type = NotificationType.reminder,
        title = '',
        message = '',
        channelId = '',
        priority = NotificationPriority.low,
        data = const {},
        actionButtons = const [],
        createdAt = DateTime.now(),
        scheduledTime = null,
        isRead = false;

  AppNotification copyWith({
    String? id,
    NotificationType? type,
    String? title,
    String? message,
    String? channelId,
    NotificationPriority? priority,
    Map<String, dynamic>? data,
    List<NotificationAction>? actionButtons,
    DateTime? createdAt,
    DateTime? scheduledTime,
    bool? isRead,
  }) {
    return AppNotification(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      channelId: channelId ?? this.channelId,
      priority: priority ?? this.priority,
      data: data ?? this.data,
      actionButtons: actionButtons ?? this.actionButtons,
      createdAt: createdAt ?? this.createdAt,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      isRead: isRead ?? this.isRead,
    );
  }
}

class NotificationAction {
  final String id;
  final String title;
  final String action;

  NotificationAction({
    required this.id,
    required this.title,
    required this.action,
  });

  NotificationAction.empty()
      : id = '',
        title = '',
        action = '';
}

// Mock data models for database queries
class LowStockProduct {
  final int id;
  final String name;
  final int currentStock;

  LowStockProduct({
    required this.id,
    required this.name,
    required this.currentStock,
  });
}

class PendingOrder {
  final int id;
  final String orderNumber;
  final DateTime createdAt;

  PendingOrder({
    required this.id,
    required this.orderNumber,
    required this.createdAt,
  });
}

class OverduePayment {
  final int id;
  final double amount;
  final String customerName;

  OverduePayment({
    required this.id,
    required this.amount,
    required this.customerName,
  });
}

class MaintenanceSchedule {
  final int id;
  final DateTime scheduledTime;
  final String description;

  MaintenanceSchedule({
    required this.id,
    required this.scheduledTime,
    required this.description,
  });
}

class ReminderItem {
  final int id;
  final String title;
  final String description;
  final DateTime dueTime;

  ReminderItem({
    required this.id,
    required this.title,
    required this.description,
    required this.dueTime,
  });
}
