
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/shared/main_shell.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:shared_preferences/shared_preferences.dart';

class EmployeeLoginForm extends StatefulWidget {
  const EmployeeLoginForm({super.key});

  @override
  State<EmployeeLoginForm> createState() => _EmployeeLoginFormState();
}

class _EmployeeLoginFormState extends State<EmployeeLoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _companyIdController = TextEditingController();
  final _employeeIdController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _rememberMe = false;

  @override
  void initState() {
    super.initState();
    _loadRememberMe();
  }

  _loadRememberMe() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      _rememberMe = prefs.getBool('rememberEmployee') ?? false;
      if (_rememberMe) {
        _companyIdController.text = prefs.getString('employeeCompanyId') ?? '';
        _employeeIdController.text = prefs.getString('employeeId') ?? '';
        _passwordController.text = prefs.getString('employeePassword') ?? '';
      }
    });
  }

  _saveRememberMe() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('rememberEmployee', _rememberMe);
    if (_rememberMe) {
      prefs.setString('employeeCompanyId', _companyIdController.text);
      prefs.setString('employeeId', _employeeIdController.text);
      prefs.setString('employeePassword', _passwordController.text);
    } else {
      prefs.remove('employeeCompanyId');
      prefs.remove('employeeId');
      prefs.remove('employeePassword');
    }
  }

  void _login() {
    if (_formKey.currentState!.validate()) {
      // Mock login logic
      if (_companyIdController.text == '123' &&
          _employeeIdController.text == 'employee' &&
          _passwordController.text == 'employee') {
        _saveRememberMe();
        Navigator.pushReplacement(context, FluentPageRoute(builder: (context) => const MainShell(child: SizedBox())));
      } else {
        showDialog(
          context: context,
          builder: (context) => ContentDialog(
            title: Text(AppLocalizations.of(context).translate('login')),
            content: Text(AppLocalizations.of(context).translate('invalidCredentials')),
            actions: [
              FilledButton(child: Text(AppLocalizations.of(context).translate('ok')), onPressed: () => Navigator.pop(context)),
            ],
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _companyIdController.dispose();
    _employeeIdController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TextFormBox(
            controller: _companyIdController,
            placeholder: AppLocalizations.of(context).translate('companyId'),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppLocalizations.of(context).translate('companyIdRequired');
              }
              return null;
            },
          ),
          const SizedBox(height: 20),
          TextFormBox(
            controller: _employeeIdController,
            placeholder: AppLocalizations.of(context).translate('employeeIdOrEmail'),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppLocalizations.of(context).translate('employeeIdOrEmailRequired');
              }
              return null;
            },
          ),
          const SizedBox(height: 20),
          TextFormBox(
            controller: _passwordController,
            placeholder: AppLocalizations.of(context).translate('password'),
            obscureText: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppLocalizations.of(context).translate('passwordRequired');
              }
              return null;
            },
          ),
          const SizedBox(height: 20),
          Checkbox(
            content: Text(AppLocalizations.of(context).translate('rememberMe')),
            checked: _rememberMe,
            onChanged: (value) {
              setState(() {
                _rememberMe = value!;
              });
            },
          ),
          const SizedBox(height: 20),
          FilledButton(
            onPressed: _login,
            child: Text(AppLocalizations.of(context).translate('login')),
          ),
        ],
      ),
    );
  }
}
