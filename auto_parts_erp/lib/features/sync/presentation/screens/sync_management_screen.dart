import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/core/services/offline_sync_service.dart';
import 'package:auto_parts_erp/core/database/database_provider.dart';

// Pending operations provider
final pendingOperationsProvider = FutureProvider<List<SyncOperation>>((ref) async {
  final database = ref.watch(databaseProvider);
  return await database.getPendingSyncOperations();
});

// Conflicts provider
final conflictsProvider = FutureProvider<List<ConflictRecord>>((ref) async {
  final database = ref.watch(databaseProvider);
  return await database.getPendingConflicts();
});

class SyncManagementScreen extends ConsumerStatefulWidget {
  const SyncManagementScreen({super.key});

  @override
  ConsumerState<SyncManagementScreen> createState() => _SyncManagementScreenState();
}

class _SyncManagementScreenState extends ConsumerState<SyncManagementScreen> {
  @override
  Widget build(BuildContext context) {
    final syncStatus = ref.watch(syncStatusProvider);
    final pendingOperations = ref.watch(pendingOperationsProvider);
    final conflicts = ref.watch(conflictsProvider);

    return ScaffoldPage(
      header: PageHeader(
        title: Text(AppLocalizations.of(context).translate('syncManagement')),
        commandBar: Row(
          children: [
            FilledButton(
              onPressed: syncStatus.isLoading ? null : () {
                ref.read(syncStatusProvider.notifier).manualSync();
              },
              child: syncStatus.isLoading
                  ? Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(
                          width: 16,
                          height: 16,
                          child: ProgressRing(),
                        ),
                        const SizedBox(width: 8),
                        Text(AppLocalizations.of(context).translate('syncing')),
                      ],
                    )
                  : Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(FluentIcons.sync),
                        const SizedBox(width: 8),
                        Text(AppLocalizations.of(context).translate('syncNow')),
                      ],
                    ),
            ),
            const SizedBox(width: 8),
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.refresh),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('refresh')),
                ],
              ),
              onPressed: () {
                ref.invalidate(pendingOperationsProvider);
                ref.invalidate(conflictsProvider);
              },
            ),
          ],
        ),
      ),
      content: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Sync Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          syncStatus.isOnline ? FluentIcons.wifi : FluentIcons.wifi_off,
                          size: 24,
                          color: syncStatus.isOnline ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          AppLocalizations.of(context).translate('connectionStatus'),
                          style: FluentTheme.of(context).typography.subtitle,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    _buildStatusItem(
                      context,
                      AppLocalizations.of(context).translate('status'),
                      syncStatus.isOnline 
                          ? AppLocalizations.of(context).translate('online')
                          : AppLocalizations.of(context).translate('offline'),
                      syncStatus.isOnline ? Colors.green : Colors.red,
                    ),
                    if (syncStatus.lastSync != null)
                      _buildStatusItem(
                        context,
                        AppLocalizations.of(context).translate('lastSync'),
                        _formatDateTime(syncStatus.lastSync!),
                      ),
                    if (syncStatus.message != null)
                      _buildStatusItem(
                        context,
                        AppLocalizations.of(context).translate('lastMessage'),
                        syncStatus.message!,
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Pending Operations
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(FluentIcons.clock, size: 24, color: Colors.orange),
                        const SizedBox(width: 8),
                        Text(
                          AppLocalizations.of(context).translate('pendingOperations'),
                          style: FluentTheme.of(context).typography.subtitle,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    pendingOperations.when(
                      data: (operations) {
                        if (operations.isEmpty) {
                          return Center(
                            child: Padding(
                              padding: const EdgeInsets.all(32.0),
                              child: Column(
                                children: [
                                  const Icon(FluentIcons.check_mark, size: 48, color: Colors.green),
                                  const SizedBox(height: 16),
                                  Text(
                                    AppLocalizations.of(context).translate('allSynced'),
                                    style: FluentTheme.of(context).typography.body,
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        return Column(
                          children: operations.map((operation) => _buildOperationItem(context, operation)).toList(),
                        );
                      },
                      loading: () => const Center(child: ProgressRing()),
                      error: (error, stack) => Center(
                        child: Text('Error loading operations: $error'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Conflicts
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(FluentIcons.warning, size: 24, color: Colors.red),
                        const SizedBox(width: 8),
                        Text(
                          AppLocalizations.of(context).translate('conflicts'),
                          style: FluentTheme.of(context).typography.subtitle,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    conflicts.when(
                      data: (conflictList) {
                        if (conflictList.isEmpty) {
                          return Center(
                            child: Padding(
                              padding: const EdgeInsets.all(32.0),
                              child: Column(
                                children: [
                                  const Icon(FluentIcons.check_mark, size: 48, color: Colors.green),
                                  const SizedBox(height: 16),
                                  Text(
                                    AppLocalizations.of(context).translate('noConflicts'),
                                    style: FluentTheme.of(context).typography.body,
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        return Column(
                          children: conflictList.map((conflict) => _buildConflictItem(context, conflict)).toList(),
                        );
                      },
                      loading: () => const Center(child: ProgressRing()),
                      error: (error, stack) => Center(
                        child: Text('Error loading conflicts: $error'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Sync Settings
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(FluentIcons.settings, size: 24, color: Colors.blue),
                        const SizedBox(width: 8),
                        Text(
                          AppLocalizations.of(context).translate('syncSettings'),
                          style: FluentTheme.of(context).typography.subtitle,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    _buildSettingItem(
                      context,
                      AppLocalizations.of(context).translate('autoSync'),
                      AppLocalizations.of(context).translate('autoSyncDescription'),
                      true,
                      (value) {
                        // TODO: Update auto sync setting
                      },
                    ),
                    _buildSettingItem(
                      context,
                      AppLocalizations.of(context).translate('syncOnWifiOnly'),
                      AppLocalizations.of(context).translate('syncOnWifiOnlyDescription'),
                      false,
                      (value) {
                        // TODO: Update wifi only setting
                      },
                    ),
                    _buildSettingItem(
                      context,
                      AppLocalizations.of(context).translate('backgroundSync'),
                      AppLocalizations.of(context).translate('backgroundSyncDescription'),
                      true,
                      (value) {
                        // TODO: Update background sync setting
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(BuildContext context, String label, String value, [Color? valueColor]) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: FluentTheme.of(context).typography.body,
          ),
          Text(
            value,
            style: FluentTheme.of(context).typography.body?.copyWith(
              fontWeight: FontWeight.bold,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOperationItem(BuildContext context, SyncOperation operation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            _getOperationIcon(operation.operationType),
            size: 20,
            color: _getOperationColor(operation.operationType),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${operation.operationType} ${operation.tableName}',
                  style: FluentTheme.of(context).typography.body?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Record ID: ${operation.recordId}',
                  style: FluentTheme.of(context).typography.caption,
                ),
                Text(
                  _formatDateTime(operation.createdAt),
                  style: FluentTheme.of(context).typography.caption,
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(FluentIcons.delete, size: 16),
            onPressed: () {
              _showDeleteOperationDialog(operation);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildConflictItem(BuildContext context, ConflictRecord conflict) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.red.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          const Icon(FluentIcons.warning, size: 20, color: Colors.red),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${conflict.conflictType} in ${conflict.tableName}',
                  style: FluentTheme.of(context).typography.body?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Record ID: ${conflict.recordId}',
                  style: FluentTheme.of(context).typography.caption,
                ),
                Text(
                  _formatDateTime(conflict.createdAt),
                  style: FluentTheme.of(context).typography.caption,
                ),
              ],
            ),
          ),
          Button(
            child: Text(AppLocalizations.of(context).translate('resolve')),
            onPressed: () {
              _showResolveConflictDialog(conflict);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem(BuildContext context, String title, String description, bool value, Function(bool) onChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: FluentTheme.of(context).typography.body?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: FluentTheme.of(context).typography.caption,
                ),
              ],
            ),
          ),
          ToggleSwitch(
            checked: value,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  IconData _getOperationIcon(String operationType) {
    switch (operationType) {
      case 'CREATE':
        return FluentIcons.add;
      case 'UPDATE':
        return FluentIcons.edit;
      case 'DELETE':
        return FluentIcons.delete;
      default:
        return FluentIcons.unknown;
    }
  }

  Color _getOperationColor(String operationType) {
    switch (operationType) {
      case 'CREATE':
        return Colors.green;
      case 'UPDATE':
        return Colors.blue;
      case 'DELETE':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showDeleteOperationDialog(SyncOperation operation) {
    showDialog(
      context: context,
      builder: (context) => ContentDialog(
        title: Text(AppLocalizations.of(context).translate('deleteOperation')),
        content: Text(AppLocalizations.of(context).translate('deleteOperationConfirmation')),
        actions: [
          Button(
            child: Text(AppLocalizations.of(context).translate('cancel')),
            onPressed: () => Navigator.of(context).pop(),
          ),
          FilledButton(
            child: Text(AppLocalizations.of(context).translate('delete')),
            onPressed: () {
              // TODO: Delete operation
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }

  void _showResolveConflictDialog(ConflictRecord conflict) {
    showDialog(
      context: context,
      builder: (context) => ContentDialog(
        title: Text(AppLocalizations.of(context).translate('resolveConflict')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(AppLocalizations.of(context).translate('conflictResolutionOptions')),
            const SizedBox(height: 16),
            Button(
              child: Text(AppLocalizations.of(context).translate('useLocalVersion')),
              onPressed: () {
                // TODO: Use local version
                Navigator.of(context).pop();
              },
            ),
            const SizedBox(height: 8),
            Button(
              child: Text(AppLocalizations.of(context).translate('useServerVersion')),
              onPressed: () {
                // TODO: Use server version
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
        actions: [
          Button(
            child: Text(AppLocalizations.of(context).translate('cancel')),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}
