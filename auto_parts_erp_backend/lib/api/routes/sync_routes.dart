import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../../services/database_service.dart';
import '../middleware/auth_middleware.dart';

class SyncRoutes {
  Router get router {
    final router = Router();

    router.post('/push', _pushData);
    router.get('/pull', _pullData);
    router.get('/status', _getSyncStatus);

    return router;
  }

  Future<Response> _pushData(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      final operations = data['operations'] as List<dynamic>?;

      if (operations == null || operations.isEmpty) {
        return Response.badRequest(
          body: jsonEncode({'error': 'No operations to sync'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final userInfo = getUserFromRequest(request);
      final results = <Map<String, dynamic>>[];

      await DatabaseService.instance.transaction(() async {
        for (final operation in operations) {
          try {
            final result = await _processOperation(operation, userInfo);
            results.add({
              'operation_id': operation['id'],
              'status': 'success',
              'result': result,
            });
          } catch (e) {
            results.add({
              'operation_id': operation['id'],
              'status': 'error',
              'error': e.toString(),
            });
          }
        }
      });

      return Response.ok(
        jsonEncode({
          'message': 'Sync completed',
          'results': results,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Sync failed: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _pullData(Request request) async {
    try {
      final lastSyncParam = request.url.queryParameters['last_sync'];
      final branchId = request.url.queryParameters['branch_id'];
      
      if (lastSyncParam == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'last_sync parameter is required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final lastSync = DateTime.parse(lastSyncParam);
      final userInfo = getUserFromRequest(request);
      final userBranchId = userInfo['branch_id'] ?? (branchId != null ? int.parse(branchId) : null);

      // Get updated data since last sync
      final updates = <String, dynamic>{};

      // Products
      final products = await DatabaseService.instance.query('''
        SELECT * FROM products 
        WHERE updated_at > @last_sync OR created_at > @last_sync
        ORDER BY updated_at DESC
      ''', {'last_sync': lastSync.toIso8601String()});
      updates['products'] = products;

      // Categories
      final categories = await DatabaseService.instance.query('''
        SELECT * FROM categories 
        WHERE updated_at > @last_sync OR created_at > @last_sync
        ORDER BY updated_at DESC
      ''', {'last_sync': lastSync.toIso8601String()});
      updates['categories'] = categories;

      // Brands
      final brands = await DatabaseService.instance.query('''
        SELECT * FROM brands 
        WHERE updated_at > @last_sync OR created_at > @last_sync
        ORDER BY updated_at DESC
      ''', {'last_sync': lastSync.toIso8601String()});
      updates['brands'] = brands;

      // Inventory for user's branch
      if (userBranchId != null) {
        final inventory = await DatabaseService.instance.query('''
          SELECT * FROM branch_inventory 
          WHERE branch_id = @branch_id AND last_updated > @last_sync
          ORDER BY last_updated DESC
        ''', {
          'branch_id': userBranchId,
          'last_sync': lastSync.toIso8601String(),
        });
        updates['inventory'] = inventory;

        // Sales for user's branch
        final sales = await DatabaseService.instance.query('''
          SELECT s.*, 
                 COALESCE(
                   json_agg(
                     json_build_object(
                       'id', si.id,
                       'product_id', si.product_id,
                       'quantity', si.quantity,
                       'unit_price', si.unit_price,
                       'total_price', si.total_price,
                       'discount_amount', si.discount_amount,
                       'notes', si.notes
                     )
                   ) FILTER (WHERE si.id IS NOT NULL), 
                   '[]'::json
                 ) as items
          FROM sales s
          LEFT JOIN sales_items si ON s.id = si.sale_id
          WHERE s.branch_id = @branch_id 
          AND (s.updated_at > @last_sync OR s.created_at > @last_sync)
          GROUP BY s.id
          ORDER BY s.updated_at DESC
        ''', {
          'branch_id': userBranchId,
          'last_sync': lastSync.toIso8601String(),
        });
        updates['sales'] = sales;

        // Purchases for user's branch
        final purchases = await DatabaseService.instance.query('''
          SELECT p.*, 
                 COALESCE(
                   json_agg(
                     json_build_object(
                       'id', pi.id,
                       'product_id', pi.product_id,
                       'quantity', pi.quantity,
                       'unit_cost', pi.unit_cost,
                       'total_cost', pi.total_cost,
                       'expiry_date', pi.expiry_date,
                       'batch_number', pi.batch_number,
                       'notes', pi.notes
                     )
                   ) FILTER (WHERE pi.id IS NOT NULL), 
                   '[]'::json
                 ) as items
          FROM purchases p
          LEFT JOIN purchase_items pi ON p.id = pi.purchase_id
          WHERE p.branch_id = @branch_id 
          AND (p.updated_at > @last_sync OR p.created_at > @last_sync)
          GROUP BY p.id
          ORDER BY p.updated_at DESC
        ''', {
          'branch_id': userBranchId,
          'last_sync': lastSync.toIso8601String(),
        });
        updates['purchases'] = purchases;
      }

      // Customers (global)
      final customers = await DatabaseService.instance.query('''
        SELECT * FROM customers 
        WHERE updated_at > @last_sync OR created_at > @last_sync
        ORDER BY updated_at DESC
      ''', {'last_sync': lastSync.toIso8601String()});
      updates['customers'] = customers;

      // Suppliers (global)
      final suppliers = await DatabaseService.instance.query('''
        SELECT * FROM suppliers 
        WHERE updated_at > @last_sync OR created_at > @last_sync
        ORDER BY updated_at DESC
      ''', {'last_sync': lastSync.toIso8601String()});
      updates['suppliers'] = suppliers;

      return Response.ok(
        jsonEncode({
          'last_sync': DateTime.now().toIso8601String(),
          'updates': updates,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to pull data: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getSyncStatus(Request request) async {
    try {
      final userInfo = getUserFromRequest(request);
      final branchId = userInfo['branch_id'];

      // Get pending sync items count
      final pendingCount = await DatabaseService.instance.queryOne('''
        SELECT COUNT(*) as count FROM sync_queue WHERE synced = false
      ''');

      // Get last sync time
      final lastSync = await DatabaseService.instance.queryOne('''
        SELECT MAX(created_at) as last_sync FROM sync_queue WHERE synced = true
      ''');

      // Get branch inventory summary
      Map<String, dynamic>? inventorySummary;
      if (branchId != null) {
        inventorySummary = await DatabaseService.instance.queryOne('''
          SELECT 
            COUNT(*) as total_products,
            SUM(quantity) as total_quantity,
            COUNT(CASE WHEN quantity <= 0 THEN 1 END) as out_of_stock,
            COUNT(CASE WHEN quantity <= p.min_stock_level THEN 1 END) as low_stock
          FROM branch_inventory bi
          JOIN products p ON bi.product_id = p.id
          WHERE bi.branch_id = @branch_id AND p.is_active = true
        ''', {'branch_id': branchId});
      }

      return Response.ok(
        jsonEncode({
          'pending_operations': pendingCount?['count'] ?? 0,
          'last_sync': lastSync?['last_sync'],
          'inventory_summary': inventorySummary,
          'server_time': DateTime.now().toIso8601String(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get sync status: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Map<String, dynamic>> _processOperation(
    Map<String, dynamic> operation,
    Map<String, dynamic> userInfo,
  ) async {
    final operationType = operation['operation_type'];
    final tableName = operation['table_name'];
    final data = operation['data'] as Map<String, dynamic>;

    switch (operationType) {
      case 'CREATE':
        return await _handleCreate(tableName, data, userInfo);
      case 'UPDATE':
        return await _handleUpdate(tableName, data, userInfo);
      case 'DELETE':
        return await _handleDelete(tableName, data, userInfo);
      default:
        throw Exception('Unknown operation type: $operationType');
    }
  }

  Future<Map<String, dynamic>> _handleCreate(
    String tableName,
    Map<String, dynamic> data,
    Map<String, dynamic> userInfo,
  ) async {
    // Add user info to data
    data['created_at'] = DateTime.now().toIso8601String();
    if (tableName == 'sales' || tableName == 'purchases') {
      data['user_id'] = userInfo['user_id'];
      data['branch_id'] ??= userInfo['branch_id'];
    }

    // Build INSERT query dynamically
    final columns = data.keys.join(', ');
    final placeholders = data.keys.map((key) => '@$key').join(', ');
    
    final result = await DatabaseService.instance.queryOne('''
      INSERT INTO $tableName ($columns) 
      VALUES ($placeholders) 
      RETURNING id
    ''', data);

    return {'id': result?['id']};
  }

  Future<Map<String, dynamic>> _handleUpdate(
    String tableName,
    Map<String, dynamic> data,
    Map<String, dynamic> userInfo,
  ) async {
    final id = data.remove('id');
    if (id == null) {
      throw Exception('ID is required for update operations');
    }

    // Add updated timestamp
    data['updated_at'] = DateTime.now().toIso8601String();

    // Build UPDATE query dynamically
    final setClause = data.keys.map((key) => '$key = @$key').join(', ');
    
    final affectedRows = await DatabaseService.instance.execute('''
      UPDATE $tableName SET $setClause WHERE id = @id
    ''', {...data, 'id': id});

    return {'affected_rows': affectedRows};
  }

  Future<Map<String, dynamic>> _handleDelete(
    String tableName,
    Map<String, dynamic> data,
    Map<String, dynamic> userInfo,
  ) async {
    final id = data['id'];
    if (id == null) {
      throw Exception('ID is required for delete operations');
    }

    // For most tables, we do soft delete by setting is_active = false
    if (['products', 'categories', 'brands', 'customers', 'suppliers'].contains(tableName)) {
      final affectedRows = await DatabaseService.instance.execute('''
        UPDATE $tableName SET is_active = false, updated_at = NOW() WHERE id = @id
      ''', {'id': id});
      return {'affected_rows': affectedRows};
    } else {
      // For other tables, do hard delete
      final affectedRows = await DatabaseService.instance.execute('''
        DELETE FROM $tableName WHERE id = @id
      ''', {'id': id});
      return {'affected_rows': affectedRows};
    }
  }
}
