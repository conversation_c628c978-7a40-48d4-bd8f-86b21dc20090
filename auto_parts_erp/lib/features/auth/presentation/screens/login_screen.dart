
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/core/services/auth_service.dart';
import 'package:auto_parts_erp/core/utils/validators.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _rememberMe = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // TODO: Fix autoLogin provider issue
    // Future(() => _checkAutoLogin());
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _checkAutoLogin() async {
    final authNotifier = ref.read(authStateProvider.notifier);
    await authNotifier.autoLogin();

    final authState = ref.read(authStateProvider);
    if (authState.isLoggedIn) {
      if (mounted) {
        context.go('/dashboard');
      }
    }
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    final authNotifier = ref.read(authStateProvider.notifier);
    await authNotifier.login(
      _usernameController.text.trim(),
      _passwordController.text,
      rememberMe: _rememberMe,
    );

    setState(() => _isLoading = false);

    final authState = ref.read(authStateProvider);
    if (authState.isLoggedIn) {
      if (mounted) {
        context.go('/dashboard');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authStateProvider);

    return ScaffoldPage(
      content: Center(
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: FluentTheme.of(context).cardColor,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Logo and title
                Icon(
                  FluentIcons.car,
                  size: 64,
                  color: FluentTheme.of(context).accentColor,
                ),
                const SizedBox(height: 16),
                Text(
                  AppLocalizations.of(context).translate('appTitle'),
                  style: FluentTheme.of(context).typography.title,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  AppLocalizations.of(context).translate('login'),
                  style: FluentTheme.of(context).typography.subtitle,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // Username field
                InfoLabel(
                  label: AppLocalizations.of(context).translate('username'),
                  child: TextFormBox(
                    controller: _usernameController,
                    placeholder: AppLocalizations.of(context).translate('enterUsername'),
                    prefix: const Icon(FluentIcons.contact),
                    validator: Validators.validateUsername,
                    enabled: !_isLoading,
                  ),
                ),
                const SizedBox(height: 16),

                // Password field
                InfoLabel(
                  label: AppLocalizations.of(context).translate('password'),
                  child: PasswordFormBox(
                    controller: _passwordController,
                    placeholder: AppLocalizations.of(context).translate('enterPassword'),
                    validator: Validators.validatePassword,
                    enabled: !_isLoading,
                  ),
                ),
                const SizedBox(height: 16),

                // Remember me checkbox
                Checkbox(
                  checked: _rememberMe,
                  onChanged: _isLoading ? null : (value) {
                    setState(() => _rememberMe = value ?? false);
                  },
                  content: Text(AppLocalizations.of(context).translate('rememberMe')),
                ),
                const SizedBox(height: 24),

                // Login button
                FilledButton(
                  onPressed: _isLoading ? null : _login,
                  child: _isLoading
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: ProgressRing(),
                            ),
                            const SizedBox(width: 8),
                            Text(AppLocalizations.of(context).translate('loggingIn')),
                          ],
                        )
                      : Text(AppLocalizations.of(context).translate('login')),
                ),

                // Error message
                if (authState.message != null && !authState.isLoggedIn) ...[
                  const SizedBox(height: 16),
                  InfoBar(
                    title: Text(AppLocalizations.of(context).translate('error')),
                    content: Text(authState.message!),
                    severity: InfoBarSeverity.error,
                    onClose: () {
                      ref.read(authStateProvider.notifier).clearMessage();
                    },
                  ),
                ],

                // Success message
                if (authState.message != null && authState.isLoggedIn) ...[
                  const SizedBox(height: 16),
                  InfoBar(
                    title: Text(AppLocalizations.of(context).translate('success')),
                    content: Text(authState.message!),
                    severity: InfoBarSeverity.success,
                  ),
                ],

                const SizedBox(height: 24),

                // Footer
                Text(
                  '© 2024 Auto Parts ERP v1.0.0',
                  style: FluentTheme.of(context).typography.caption,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
