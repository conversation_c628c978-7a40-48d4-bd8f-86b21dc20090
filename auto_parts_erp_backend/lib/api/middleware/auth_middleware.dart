import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:jaguar_jwt/jaguar_jwt.dart';
import 'package:dotenv/dotenv.dart';

Middleware authMiddleware() {
  return (Handler innerHandler) {
    return (Request request) async {
      // Skip auth for certain routes
      final path = request.url.path;
      if (_isPublicRoute(path)) {
        return innerHandler(request);
      }

      // Extract token from Authorization header
      final authHeader = request.headers['authorization'];
      if (authHeader == null || !authHeader.startsWith('Bearer ')) {
        return Response.unauthorized(
          jsonEncode({'error': 'Missing or invalid authorization header'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final token = authHeader.substring(7); // Remove 'Bearer ' prefix

      try {
        // Verify JWT token
        final env = DotEnv()..load();
        final jwtSecret = env['JWT_SECRET'] ?? 'your-secret-key';
        
        final claimSet = verifyJwtHS256Signature(token, jwtSecret);
        
        // Check if token is expired
        if (claimSet.expiry != null && 
            claimSet.expiry!.isBefore(DateTime.now())) {
          return Response.unauthorized(
            jsonEncode({'error': 'Token expired'}),
            headers: {'Content-Type': 'application/json'},
          );
        }

        // Add user info to request context
        final updatedRequest = request.change(context: {
          ...request.context,
          'user_id': claimSet.subject,
          'username': claimSet['username'],
          'role': claimSet['role'],
          'branch_id': claimSet['branch_id'],
        });

        return innerHandler(updatedRequest);
      } catch (e) {
        return Response.unauthorized(
          jsonEncode({'error': 'Invalid token'}),
          headers: {'Content-Type': 'application/json'},
        );
      }
    };
  };
}

bool _isPublicRoute(String path) {
  final publicRoutes = [
    'auth/login',
    'auth/refresh',
    'health',
    '',
  ];
  
  return publicRoutes.any((route) => path.startsWith(route));
}

// Helper function to extract user info from request
Map<String, dynamic> getUserFromRequest(Request request) {
  return {
    'user_id': request.context['user_id'],
    'username': request.context['username'],
    'role': request.context['role'],
    'branch_id': request.context['branch_id'],
  };
}

// Helper function to check if user has required role
bool hasRole(Request request, String requiredRole) {
  final userRole = request.context['role'] as String?;
  
  // Admin can access everything
  if (userRole == 'admin') return true;
  
  // Check specific role
  return userRole == requiredRole;
}

// Helper function to check if user has any of the required roles
bool hasAnyRole(Request request, List<String> requiredRoles) {
  final userRole = request.context['role'] as String?;
  
  // Admin can access everything
  if (userRole == 'admin') return true;
  
  // Check if user has any of the required roles
  return requiredRoles.contains(userRole);
}
