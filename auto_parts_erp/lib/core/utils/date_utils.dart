import 'package:intl/intl.dart';

class AppDateUtils {
  // Date formatters
  static final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');
  static final DateFormat _dateTimeFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
  static final DateFormat _displayDateFormat = DateFormat('dd/MM/yyyy');
  static final DateFormat _displayDateTimeFormat = DateFormat('dd/MM/yyyy HH:mm');
  static final DateFormat _arabicDateFormat = DateFormat('dd/MM/yyyy', 'ar');
  static final DateFormat _timeFormat = DateFormat('HH:mm');
  
  // Format date for database storage
  static String formatForDatabase(DateTime date) {
    return _dateFormat.format(date);
  }
  
  // Format datetime for database storage
  static String formatDateTimeForDatabase(DateTime dateTime) {
    return _dateTimeFormat.format(dateTime);
  }
  
  // Format date for display
  static String formatForDisplay(DateTime date, {bool isArabic = false}) {
    if (isArabic) {
      return _arabicDateFormat.format(date);
    }
    return _displayDateFormat.format(date);
  }
  
  // Format datetime for display
  static String formatDateTimeForDisplay(DateTime dateTime, {bool isArabic = false}) {
    if (isArabic) {
      return '${_arabicDateFormat.format(dateTime)} ${_timeFormat.format(dateTime)}';
    }
    return _displayDateTimeFormat.format(dateTime);
  }
  
  // Format time only
  static String formatTime(DateTime dateTime) {
    return _timeFormat.format(dateTime);
  }
  
  // Parse date from string
  static DateTime? parseDate(String dateString) {
    try {
      return _dateFormat.parse(dateString);
    } catch (e) {
      return null;
    }
  }
  
  // Parse datetime from string
  static DateTime? parseDateTime(String dateTimeString) {
    try {
      return _dateTimeFormat.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }
  
  // Get start of day
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }
  
  // Get end of day
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }
  
  // Get start of month
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }
  
  // Get end of month
  static DateTime endOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0, 23, 59, 59, 999);
  }
  
  // Get start of year
  static DateTime startOfYear(DateTime date) {
    return DateTime(date.year, 1, 1);
  }
  
  // Get end of year
  static DateTime endOfYear(DateTime date) {
    return DateTime(date.year, 12, 31, 23, 59, 59, 999);
  }
  
  // Check if date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }
  
  // Check if date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year && 
           date.month == yesterday.month && 
           date.day == yesterday.day;
  }
  
  // Get days between two dates
  static int daysBetween(DateTime start, DateTime end) {
    return end.difference(start).inDays;
  }
  
  // Get relative time string (e.g., "2 hours ago", "yesterday")
  static String getRelativeTime(DateTime dateTime, {bool isArabic = false}) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return isArabic ? 'أمس' : 'Yesterday';
      } else if (difference.inDays < 7) {
        return isArabic ? '${difference.inDays} أيام مضت' : '${difference.inDays} days ago';
      } else {
        return formatForDisplay(dateTime, isArabic: isArabic);
      }
    } else if (difference.inHours > 0) {
      return isArabic ? '${difference.inHours} ساعات مضت' : '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return isArabic ? '${difference.inMinutes} دقائق مضت' : '${difference.inMinutes} minutes ago';
    } else {
      return isArabic ? 'الآن' : 'Just now';
    }
  }
  
  // Get age from birth date
  static int getAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }
  
  // Get week number of year
  static int getWeekOfYear(DateTime date) {
    final startOfYear = DateTime(date.year, 1, 1);
    final dayOfYear = date.difference(startOfYear).inDays + 1;
    return ((dayOfYear - date.weekday + 10) / 7).floor();
  }
  
  // Get quarter of year
  static int getQuarter(DateTime date) {
    return ((date.month - 1) / 3).floor() + 1;
  }
  
  // Check if year is leap year
  static bool isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }
  
  // Get days in month
  static int getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }
}
