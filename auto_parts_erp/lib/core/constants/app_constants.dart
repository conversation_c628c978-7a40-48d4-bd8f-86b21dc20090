class AppConstants {
  // App Information
  static const String appName = 'Auto Parts ERP';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Comprehensive ERP system for auto parts companies';
  
  // Database
  static const String databaseName = 'auto_parts_erp.db';
  static const int databaseVersion = 1;
  
  // Shared Preferences Keys
  static const String keyLanguage = 'language';
  static const String keyThemeMode = 'theme_mode';
  static const String keyRememberMe = 'remember_me';
  static const String keyLastUsername = 'last_username';
  static const String keyAuthToken = 'auth_token';
  static const String keyRefreshToken = 'refresh_token';
  static const String keyCurrentBranch = 'current_branch';
  static const String keyLastSyncTime = 'last_sync_time';
  
  // User Roles
  static const String roleAdmin = 'admin';
  static const String roleManager = 'manager';
  static const String roleEmployee = 'employee';
  static const String roleCashier = 'cashier';
  
  // Product Units
  static const List<String> productUnits = [
    'piece',
    'kg',
    'liter',
    'meter',
    'box',
    'pack',
    'set',
    'pair',
  ];
  
  // Payment Methods
  static const List<String> paymentMethods = [
    'CASH',
    'CARD',
    'BANK_TRANSFER',
    'CREDIT',
    'CHECK',
  ];
  
  // Sale Status
  static const String saleStatusDraft = 'DRAFT';
  static const String saleStatusCompleted = 'COMPLETED';
  static const String saleStatusCancelled = 'CANCELLED';
  static const String saleStatusReturned = 'RETURNED';
  
  // Purchase Status
  static const String purchaseStatusPending = 'PENDING';
  static const String purchaseStatusReceived = 'RECEIVED';
  static const String purchaseStatusCompleted = 'COMPLETED';
  static const String purchaseStatusCancelled = 'CANCELLED';
  
  // Inventory Movement Types
  static const String movementTypeIn = 'IN';
  static const String movementTypeOut = 'OUT';
  static const String movementTypeTransfer = 'TRANSFER';
  static const String movementTypeAdjustment = 'ADJUSTMENT';
  
  // Sync Operations
  static const String syncOperationCreate = 'CREATE';
  static const String syncOperationUpdate = 'UPDATE';
  static const String syncOperationDelete = 'DELETE';
  
  // File Extensions
  static const List<String> imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp'];
  static const List<String> documentExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx'];
  
  // Validation
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 50;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayDateTimeFormat = 'dd/MM/yyyy HH:mm';
  
  // Decimal Places
  static const int priceDecimalPlaces = 2;
  static const int quantityDecimalPlaces = 0;
  static const int percentageDecimalPlaces = 2;
  
  // Default Values
  static const double defaultTaxRate = 0.15; // 15% VAT
  static const int defaultCreditDays = 30;
  static const int defaultMinStockLevel = 5;
  
  // Car Makes (Common ones)
  static const List<String> carMakes = [
    'Toyota',
    'Honda',
    'Nissan',
    'Hyundai',
    'Kia',
    'Ford',
    'Chevrolet',
    'BMW',
    'Mercedes-Benz',
    'Audi',
    'Volkswagen',
    'Mazda',
    'Mitsubishi',
    'Suzuki',
    'Lexus',
    'Infiniti',
    'Acura',
    'Subaru',
    'Volvo',
    'Peugeot',
    'Renault',
    'Citroën',
    'Fiat',
    'Alfa Romeo',
    'Jeep',
    'Land Rover',
    'Jaguar',
    'Porsche',
    'Tesla',
    'Genesis',
  ];
  
  // Product Categories
  static const List<String> defaultCategories = [
    'Engine Parts',
    'Brake System',
    'Suspension',
    'Transmission',
    'Electrical',
    'Cooling System',
    'Exhaust System',
    'Fuel System',
    'Steering',
    'Body Parts',
    'Interior',
    'Oils & Fluids',
    'Filters',
    'Tires',
    'Batteries',
    'Tools',
    'Accessories',
  ];
}
