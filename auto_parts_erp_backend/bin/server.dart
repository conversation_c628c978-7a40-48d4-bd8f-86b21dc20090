
import 'dart:io';
import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart' as io;
import 'package:shelf_router/shelf_router.dart';
import 'package:dotenv/dotenv.dart';
import '../lib/api/routes/auth_routes.dart';
import '../lib/api/routes/product_routes.dart';
import '../lib/api/routes/inventory_routes.dart';
import '../lib/api/routes/sales_routes.dart';
import '../lib/api/routes/purchase_routes.dart';
import '../lib/api/routes/sync_routes.dart';
import '../lib/api/middleware/cors_middleware.dart';
import '../lib/api/middleware/auth_middleware.dart';
import '../lib/services/database_service.dart';

void main(List<String> args) async {
  // Load environment variables
  final env = DotEnv()..load();

  // Initialize database
  await DatabaseService.instance.initialize();

  final app = Router();

  // Health check
  app.get('/', (Request request) {
    return Response.ok('Auto Parts ERP Backend API v1.0.0');
  });

  app.get('/health', (Request request) {
    return Response.ok('OK');
  });

  // Mount API routes
  app.mount('/api/auth', AuthRoutes().router);
  app.mount('/api/products', ProductRoutes().router);
  app.mount('/api/inventory', InventoryRoutes().router);
  app.mount('/api/sales', SalesRoutes().router);
  app.mount('/api/purchases', PurchaseRoutes().router);
  app.mount('/api/sync', SyncRoutes().router);

  // Create pipeline with middleware
  final handler = Pipeline()
      .addMiddleware(corsMiddleware())
      .addMiddleware(logRequests())
      .addMiddleware(authMiddleware())
      .addHandler(app);

  final port = int.parse(env['PORT'] ?? '8080');
  final server = await io.serve(handler, '0.0.0.0', port);

  print('🚀 Auto Parts ERP Backend started');
  print('📡 Server listening on http://localhost:${server.port}');
  print('🗄️  Database: ${env['DATABASE_URL']}');
  print('📊 Redis: ${env['REDIS_URL']}');
}
