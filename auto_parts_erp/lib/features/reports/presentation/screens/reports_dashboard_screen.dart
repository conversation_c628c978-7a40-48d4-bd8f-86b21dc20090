import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:go_router/go_router.dart';
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/core/database/database_provider.dart';

// Dashboard data providers
final salesSummaryProvider = FutureProvider<SalesSummary>((ref) async {
  // Mock data for now
  return SalesSummary(
    todaySales: 1500.0,
    monthSales: 45000.0,
    yearSales: 540000.0,
    totalOrders: 156,
    averageOrderValue: 288.46,
  );
});

final inventorySummaryProvider = FutureProvider<InventorySummary>((ref) async {
  // Mock data for now
  return InventorySummary(
    totalProducts: 1250,
    lowStockProducts: 23,
    totalInventoryValue: 125000.0,
    outOfStockProducts: 5,
  );
});

final topProductsProvider = FutureProvider<List<TopProduct>>((ref) async {
  // Mock data for now
  return [
    TopProduct(productName: 'Engine Oil', quantitySold: 45, revenue: 2250.0),
    TopProduct(productName: 'Brake Pads', quantitySold: 32, revenue: 1920.0),
    TopProduct(productName: 'Air Filter', quantitySold: 28, revenue: 1400.0),
    TopProduct(productName: 'Spark Plugs', quantitySold: 25, revenue: 1250.0),
    TopProduct(productName: 'Oil Filter', quantitySold: 22, revenue: 1100.0),
  ];
});

final monthlySalesProvider = FutureProvider<List<MonthlySales>>((ref) async {
  // Mock data for now
  return [
    MonthlySales(month: 'Jan', sales: 35000.0),
    MonthlySales(month: 'Feb', sales: 42000.0),
    MonthlySales(month: 'Mar', sales: 38000.0),
    MonthlySales(month: 'Apr', sales: 45000.0),
    MonthlySales(month: 'May', sales: 48000.0),
    MonthlySales(month: 'Jun', sales: 52000.0),
  ];
});

// Data models
class SalesSummary {
  final double todaySales;
  final double monthSales;
  final double yearSales;
  final int totalOrders;
  final double averageOrderValue;

  SalesSummary({
    required this.todaySales,
    required this.monthSales,
    required this.yearSales,
    required this.totalOrders,
    required this.averageOrderValue,
  });
}

class InventorySummary {
  final int totalProducts;
  final int lowStockProducts;
  final double totalInventoryValue;
  final int outOfStockProducts;

  InventorySummary({
    required this.totalProducts,
    required this.lowStockProducts,
    required this.totalInventoryValue,
    required this.outOfStockProducts,
  });
}

class TopProduct {
  final String productName;
  final int quantitySold;
  final double revenue;

  TopProduct({
    required this.productName,
    required this.quantitySold,
    required this.revenue,
  });
}

class MonthlySales {
  final String month;
  final double sales;

  MonthlySales({
    required this.month,
    required this.sales,
  });
}

class ReportsDashboardScreen extends ConsumerWidget {
  const ReportsDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final salesSummary = ref.watch(salesSummaryProvider);
    final inventorySummary = ref.watch(inventorySummaryProvider);
    final topProducts = ref.watch(topProductsProvider);
    final monthlySales = ref.watch(monthlySalesProvider);

    return ScaffoldPage(
      header: PageHeader(
        title: Text(AppLocalizations.of(context).translate('reportsDashboard')),
        commandBar: Row(
          children: [
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.chart_series),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('salesReports')),
                ],
              ),
              onPressed: () {
                context.go('/reports/sales');
              },
            ),
            const SizedBox(width: 8),
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.package),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('inventoryReports')),
                ],
              ),
              onPressed: () {
                context.go('/reports/inventory');
              },
            ),
            const SizedBox(width: 8),
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.money),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('financialReports')),
                ],
              ),
              onPressed: () {
                context.go('/reports/financial');
              },
            ),
          ],
        ),
      ),
      content: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Summary Cards Row
            Row(
              children: [
                // Sales Summary
                Expanded(
                  child: salesSummary.when(
                    data: (summary) => _buildSalesSummaryCard(context, summary),
                    loading: () => _buildLoadingCard(context),
                    error: (error, stack) => _buildErrorCard(context, 'Sales Summary'),
                  ),
                ),
                const SizedBox(width: 16),
                
                // Inventory Summary
                Expanded(
                  child: inventorySummary.when(
                    data: (summary) => _buildInventorySummaryCard(context, summary),
                    loading: () => _buildLoadingCard(context),
                    error: (error, stack) => _buildErrorCard(context, 'Inventory Summary'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Charts Row
            Row(
              children: [
                // Monthly Sales Chart
                Expanded(
                  flex: 2,
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).translate('monthlySales'),
                            style: FluentTheme.of(context).typography.subtitle,
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 300,
                            child: monthlySales.when(
                              data: (data) => _buildMonthlySalesChart(data),
                              loading: () => const Center(child: ProgressRing()),
                              error: (error, stack) => Center(
                                child: Text('Error loading chart: $error'),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                
                // Top Products
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).translate('topProducts'),
                            style: FluentTheme.of(context).typography.subtitle,
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 300,
                            child: topProducts.when(
                              data: (products) => _buildTopProductsList(context, products),
                              loading: () => const Center(child: ProgressRing()),
                              error: (error, stack) => Center(
                                child: Text('Error loading products: $error'),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Quick Actions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('quickActions'),
                      style: FluentTheme.of(context).typography.subtitle,
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 16,
                      runSpacing: 16,
                      children: [
                        _buildQuickActionButton(
                          context,
                          FluentIcons.print,
                          AppLocalizations.of(context).translate('printReports'),
                          () {
                            // TODO: Print reports
                          },
                        ),
                        _buildQuickActionButton(
                          context,
                          FluentIcons.export,
                          AppLocalizations.of(context).translate('exportData'),
                          () {
                            // TODO: Export data
                          },
                        ),
                        _buildQuickActionButton(
                          context,
                          FluentIcons.calendar,
                          AppLocalizations.of(context).translate('scheduleReports'),
                          () {
                            // TODO: Schedule reports
                          },
                        ),
                        _buildQuickActionButton(
                          context,
                          FluentIcons.settings,
                          AppLocalizations.of(context).translate('reportSettings'),
                          () {
                            // TODO: Report settings
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesSummaryCard(BuildContext context, SalesSummary summary) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(FluentIcons.money, size: 24, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).translate('salesSummary'),
                  style: FluentTheme.of(context).typography.subtitle,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSummaryItem(
              context,
              AppLocalizations.of(context).translate('todaySales'),
              '\$${summary.todaySales.toStringAsFixed(2)}',
            ),
            _buildSummaryItem(
              context,
              AppLocalizations.of(context).translate('monthSales'),
              '\$${summary.monthSales.toStringAsFixed(2)}',
            ),
            _buildSummaryItem(
              context,
              AppLocalizations.of(context).translate('yearSales'),
              '\$${summary.yearSales.toStringAsFixed(2)}',
            ),
            _buildSummaryItem(
              context,
              AppLocalizations.of(context).translate('totalOrders'),
              summary.totalOrders.toString(),
            ),
            _buildSummaryItem(
              context,
              AppLocalizations.of(context).translate('averageOrderValue'),
              '\$${summary.averageOrderValue.toStringAsFixed(2)}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInventorySummaryCard(BuildContext context, InventorySummary summary) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(FluentIcons.package, size: 24, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).translate('inventorySummary'),
                  style: FluentTheme.of(context).typography.subtitle,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSummaryItem(
              context,
              AppLocalizations.of(context).translate('totalProducts'),
              summary.totalProducts.toString(),
            ),
            _buildSummaryItem(
              context,
              AppLocalizations.of(context).translate('lowStockProducts'),
              summary.lowStockProducts.toString(),
              valueColor: summary.lowStockProducts > 0 ? Colors.orange : null,
            ),
            _buildSummaryItem(
              context,
              AppLocalizations.of(context).translate('outOfStockProducts'),
              summary.outOfStockProducts.toString(),
              valueColor: summary.outOfStockProducts > 0 ? Colors.red : null,
            ),
            _buildSummaryItem(
              context,
              AppLocalizations.of(context).translate('totalInventoryValue'),
              '\$${summary.totalInventoryValue.toStringAsFixed(2)}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(BuildContext context, String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: FluentTheme.of(context).typography.body,
          ),
          Text(
            value,
            style: FluentTheme.of(context).typography.body?.copyWith(
              fontWeight: FontWeight.bold,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlySalesChart(List<MonthlySales> data) {
    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: true),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() < data.length) {
                  return Text(data[value.toInt()].month);
                }
                return const Text('');
              },
            ),
          ),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: data.asMap().entries.map((entry) {
              return FlSpot(entry.key.toDouble(), entry.value.sales);
            }).toList(),
            isCurved: true,
            color: Colors.blue,
            barWidth: 3,
            dotData: const FlDotData(show: true),
          ),
        ],
      ),
    );
  }

  Widget _buildTopProductsList(BuildContext context, List<TopProduct> products) {
    return ListView.builder(
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: Colors.blue.withValues(alpha: 0.1),
            child: Text('${index + 1}'),
          ),
          title: Text(
            product.productName,
            style: FluentTheme.of(context).typography.body?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          subtitle: Text('${product.quantitySold} sold'),
          trailing: Text(
            '\$${product.revenue.toStringAsFixed(2)}',
            style: FluentTheme.of(context).typography.body?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickActionButton(BuildContext context, IconData icon, String label, VoidCallback onPressed) {
    return SizedBox(
      width: 150,
      child: Button(
        onPressed: onPressed,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 24),
            const SizedBox(height: 8),
            Text(
              label,
              textAlign: TextAlign.center,
              style: FluentTheme.of(context).typography.caption,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingCard(BuildContext context) {
    return Card(
      child: Container(
        height: 200,
        child: const Center(child: ProgressRing()),
      ),
    );
  }

  Widget _buildErrorCard(BuildContext context, String title) {
    return Card(
      child: Container(
        height: 200,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(FluentIcons.error, size: 32, color: Colors.red),
              const SizedBox(height: 8),
              Text('Error loading $title'),
            ],
          ),
        ),
      ),
    );
  }
}
