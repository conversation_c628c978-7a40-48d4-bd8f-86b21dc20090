// Sync-related models

class SyncOperation {
  final int id;
  final String operationType;
  final String tableName;
  final int recordId;
  final String? payload;
  final bool synced;
  final DateTime createdAt;

  SyncOperation({
    required this.id,
    required this.operationType,
    required this.tableName,
    required this.recordId,
    this.payload,
    required this.synced,
    required this.createdAt,
  });

  factory SyncOperation.fromSyncQueueData(dynamic data) {
    return SyncOperation(
      id: data.id,
      operationType: data.operationType,
      tableName: data.tableNameField,
      recordId: data.recordId,
      payload: data.payload,
      synced: data.synced,
      createdAt: data.createdAt,
    );
  }
}

class ConflictRecord {
  final int id;
  final String tableName;
  final int recordId;
  final Map<String, dynamic> localData;
  final Map<String, dynamic> serverData;
  final DateTime conflictTime;

  ConflictRecord({
    required this.id,
    required this.tableName,
    required this.recordId,
    required this.localData,
    required this.serverData,
    required this.conflictTime,
  });
}

class SyncResult {
  final bool success;
  final String message;
  final int? syncedCount;
  final int? failedCount;

  SyncResult({
    required this.success,
    required this.message,
    this.syncedCount,
    this.failedCount,
  });

  factory SyncResult.success(String message, {int? syncedCount}) {
    return SyncResult(
      success: true,
      message: message,
      syncedCount: syncedCount,
    );
  }

  factory SyncResult.error(String message, {int? failedCount}) {
    return SyncResult(
      success: false,
      message: message,
      failedCount: failedCount,
    );
  }
}

class SyncStatus {
  final bool isOnline;
  final bool isSyncing;
  final DateTime? lastSyncTime;
  final int pendingOperations;
  final int conflicts;

  SyncStatus({
    required this.isOnline,
    required this.isSyncing,
    this.lastSyncTime,
    required this.pendingOperations,
    required this.conflicts,
  });
}
