class Validators {
  // Email validation
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }
  
  // Password validation
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    
    if (value.length < 6) {
      return 'Password must be at least 6 characters long';
    }
    
    if (value.length > 50) {
      return 'Password must be less than 50 characters';
    }
    
    return null;
  }
  
  // Username validation
  static String? validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return 'Username is required';
    }
    
    if (value.length < 3) {
      return 'Username must be at least 3 characters long';
    }
    
    if (value.length > 50) {
      return 'Username must be less than 50 characters';
    }
    
    final usernameRegex = RegExp(r'^[a-zA-Z0-9_]+$');
    if (!usernameRegex.hasMatch(value)) {
      return 'Username can only contain letters, numbers, and underscores';
    }
    
    return null;
  }
  
  // Required field validation
  static String? validateRequired(String? value, {String fieldName = 'Field'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }
  
  // Phone number validation
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Phone is optional in most cases
    }
    
    final phoneRegex = RegExp(r'^\+?[1-9]\d{1,14}$');
    if (!phoneRegex.hasMatch(value.replaceAll(RegExp(r'[\s\-\(\)]'), ''))) {
      return 'Please enter a valid phone number';
    }
    
    return null;
  }
  
  // Number validation
  static String? validateNumber(String? value, {bool isRequired = false}) {
    if (value == null || value.isEmpty) {
      return isRequired ? 'Number is required' : null;
    }
    
    if (double.tryParse(value) == null) {
      return 'Please enter a valid number';
    }
    
    return null;
  }
  
  // Positive number validation
  static String? validatePositiveNumber(String? value, {bool isRequired = false}) {
    final numberValidation = validateNumber(value, isRequired: isRequired);
    if (numberValidation != null) return numberValidation;
    
    if (value != null && value.isNotEmpty) {
      final number = double.parse(value);
      if (number < 0) {
        return 'Number must be positive';
      }
    }
    
    return null;
  }
  
  // Integer validation
  static String? validateInteger(String? value, {bool isRequired = false}) {
    if (value == null || value.isEmpty) {
      return isRequired ? 'Integer is required' : null;
    }
    
    if (int.tryParse(value) == null) {
      return 'Please enter a valid integer';
    }
    
    return null;
  }
  
  // Positive integer validation
  static String? validatePositiveInteger(String? value, {bool isRequired = false}) {
    final integerValidation = validateInteger(value, isRequired: isRequired);
    if (integerValidation != null) return integerValidation;
    
    if (value != null && value.isNotEmpty) {
      final number = int.parse(value);
      if (number < 0) {
        return 'Number must be positive';
      }
    }
    
    return null;
  }
  
  // Price validation
  static String? validatePrice(String? value, {bool isRequired = false}) {
    final numberValidation = validatePositiveNumber(value, isRequired: isRequired);
    if (numberValidation != null) return numberValidation;
    
    if (value != null && value.isNotEmpty) {
      final price = double.parse(value);
      if (price > 999999.99) {
        return 'Price is too high';
      }
    }
    
    return null;
  }
  
  // Quantity validation
  static String? validateQuantity(String? value, {bool isRequired = false}) {
    final integerValidation = validatePositiveInteger(value, isRequired: isRequired);
    if (integerValidation != null) return integerValidation;
    
    if (value != null && value.isNotEmpty) {
      final quantity = int.parse(value);
      if (quantity > 999999) {
        return 'Quantity is too high';
      }
    }
    
    return null;
  }
  
  // Barcode validation
  static String? validateBarcode(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Barcode is optional
    }
    
    if (value.length < 8 || value.length > 14) {
      return 'Barcode must be between 8 and 14 characters';
    }
    
    final barcodeRegex = RegExp(r'^[0-9]+$');
    if (!barcodeRegex.hasMatch(value)) {
      return 'Barcode can only contain numbers';
    }
    
    return null;
  }
  
  // SKU validation
  static String? validateSKU(String? value) {
    if (value == null || value.isEmpty) {
      return null; // SKU is optional
    }
    
    if (value.length > 50) {
      return 'SKU must be less than 50 characters';
    }
    
    final skuRegex = RegExp(r'^[a-zA-Z0-9\-_]+$');
    if (!skuRegex.hasMatch(value)) {
      return 'SKU can only contain letters, numbers, hyphens, and underscores';
    }
    
    return null;
  }
  
  // Tax number validation
  static String? validateTaxNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Tax number is optional
    }
    
    if (value.length < 10 || value.length > 15) {
      return 'Tax number must be between 10 and 15 characters';
    }
    
    final taxRegex = RegExp(r'^[0-9]+$');
    if (!taxRegex.hasMatch(value)) {
      return 'Tax number can only contain numbers';
    }
    
    return null;
  }
  
  // URL validation
  static String? validateURL(String? value) {
    if (value == null || value.isEmpty) {
      return null; // URL is optional
    }
    
    final urlRegex = RegExp(
      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$'
    );
    
    if (!urlRegex.hasMatch(value)) {
      return 'Please enter a valid URL';
    }
    
    return null;
  }
  
  // Confirm password validation
  static String? validateConfirmPassword(String? value, String? originalPassword) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    
    if (value != originalPassword) {
      return 'Passwords do not match';
    }
    
    return null;
  }
  
  // Length validation
  static String? validateLength(String? value, {
    required int minLength,
    required int maxLength,
    String fieldName = 'Field',
  }) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    
    if (value.length < minLength) {
      return '$fieldName must be at least $minLength characters long';
    }
    
    if (value.length > maxLength) {
      return '$fieldName must be less than $maxLength characters';
    }
    
    return null;
  }
}
