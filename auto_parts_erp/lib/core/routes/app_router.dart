import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/auth/presentation/screens/login_screen.dart';
import '../../shared/main_shell.dart';
import '../../features/dashboard/presentation/screens/dashboard_main_screen.dart';
import '../../features/products/presentation/screens/product_list_screen.dart';
import '../../features/products/presentation/screens/add_product_screen.dart';
import '../../features/inventory/presentation/screens/inventory_screen.dart';

import '../../features/settings/presentation/screens/settings_screen.dart';

// Router provider
final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/login',
    routes: [
      // Auth routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      
      // Main shell with nested routes
      ShellRoute(
        builder: (context, state, child) => MainShell(child: child),
        routes: [
          // Dashboard
          GoRoute(
            path: '/dashboard',
            name: 'dashboard',
            builder: (context, state) => const DashboardMainScreen(),
          ),
          
          // Products routes
          GoRoute(
            path: '/products',
            name: 'products',
            builder: (context, state) => const ProductListScreen(),
            routes: [
              GoRoute(
                path: '/add',
                name: 'add-product',
                builder: (context, state) => const AddProductScreen(),
              ),
              GoRoute(
                path: '/:id',
                name: 'product-details',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return ProductDetailsScreen(productId: int.parse(id));
                },
              ),
            ],
          ),
          
          // Inventory routes
          GoRoute(
            path: '/inventory',
            name: 'inventory',
            builder: (context, state) => const InventoryScreen(),
          ),
          
          // Sales routes
          GoRoute(
            path: '/sales',
            name: 'sales',
            builder: (context, state) => const SalesScreen(),
            routes: [
              GoRoute(
                path: '/pos',
                name: 'pos',
                builder: (context, state) => const POSScreen(),
              ),
            ],
          ),
          
          // Purchases routes
          GoRoute(
            path: '/purchases',
            name: 'purchases',
            builder: (context, state) => const PurchasesScreen(),
          ),
          
          // Customers routes
          GoRoute(
            path: '/customers',
            name: 'customers',
            builder: (context, state) => const CustomersScreen(),
          ),
          
          // Suppliers routes
          GoRoute(
            path: '/suppliers',
            name: 'suppliers',
            builder: (context, state) => const SuppliersScreen(),
          ),
          
          // Reports routes
          GoRoute(
            path: '/reports',
            name: 'reports',
            builder: (context, state) => const ReportsScreen(),
          ),
          
          // Settings routes
          GoRoute(
            path: '/settings',
            name: 'settings',
            builder: (context, state) => const SettingsScreen(),
          ),
        ],
      ),
    ],
    redirect: (context, state) {
      // TODO: Implement authentication check
      // For now, allow all routes
      return null;
    },
  );
});

// Placeholder screens for routes that don't exist yet
class ProductDetailsScreen extends StatelessWidget {
  final int productId;
  
  const ProductDetailsScreen({super.key, required this.productId});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Product Details - $productId')),
      body: Center(child: Text('Product ID: $productId')),
    );
  }
}

class SalesScreen extends StatelessWidget {
  const SalesScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('Sales Screen')),
    );
  }
}

class POSScreen extends StatelessWidget {
  const POSScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('POS Screen')),
    );
  }
}

class PurchasesScreen extends StatelessWidget {
  const PurchasesScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('Purchases Screen')),
    );
  }
}

class CustomersScreen extends StatelessWidget {
  const CustomersScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('Customers Screen')),
    );
  }
}

class SuppliersScreen extends StatelessWidget {
  const SuppliersScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('Suppliers Screen')),
    );
  }
}

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('Reports Screen')),
    );
  }
}
