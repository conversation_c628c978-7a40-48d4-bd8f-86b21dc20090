{"appTitle": "Auto Parts ERP", "login": "<PERSON><PERSON>", "username": "Username", "password": "Password", "enterUsername": "Enter username", "enterPassword": "Enter password", "rememberMe": "Remember Me", "loggingIn": "Logging in...", "error": "Error", "success": "Success", "dashboard": "Dashboard", "products": "Products", "searchProducts": "Search products...", "addProduct": "Add Product", "noProductsFound": "No products found", "addFirstProduct": "Add your first product to get started", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "sku": "SKU", "price": "Price", "actions": "Actions", "errorLoadingProducts": "Error loading products", "retry": "Retry", "save": "Save", "cancel": "Cancel", "saving": "Saving...", "basicInformation": "Basic Information", "enterProductNameEn": "Enter product name in English", "enterProductNameAr": "Enter product name in Arabic", "enterSku": "Enter SKU", "enterBarcode": "Enter barcode", "costPrice": "Cost Price", "sellingPrice": "Selling <PERSON>", "productAddedSuccessfully": "Product added successfully", "failedToAddProduct": "Failed to add product", "inventoryManagement": "Inventory Management", "stockAdjustment": "Stock Adjustment", "refresh": "Refresh", "searchInventory": "Search inventory...", "noInventoryFound": "No inventory found", "addProductsToSeeInventory": "Add products to see inventory", "productName": "Product Name", "quantity": "Quantity", "reserved": "Reserved", "available": "Available", "avgCost": "Avg Cost", "totalValue": "Total Value", "errorLoadingInventory": "Error loading inventory", "pointOfSale": "Point of Sale", "clearCart": "Clear Cart", "checkout": "Checkout", "scanBarcode": "Scan barcode or enter manually", "cart": "<PERSON><PERSON>", "items": "items", "selectCustomer": "Select customer (optional)", "cartEmpty": "Cart is empty", "addProductsToCart": "Add products to cart to start a sale", "completeSale": "Complete Sale", "salesManagement": "Sales Management", "newSale": "New Sale", "searchSales": "Search sales...", "allStatuses": "All Statuses", "dateFilter": "Date Filter", "noSalesFound": "No sales found", "createFirstSale": "Create your first sale to get started", "invoiceNumber": "Invoice Number", "customer": "Customer", "date": "Date", "total": "Total", "paid": "Paid", "status": "Status", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "refunded": "Refunded", "errorLoadingSales": "Error loading sales", "selectDateRange": "Select Date Range", "apply": "Apply", "purchasesManagement": "Purchases Management", "newPurchaseOrder": "New Purchase Order", "searchPurchases": "Search purchases...", "noPurchasesFound": "No purchases found", "createFirstPurchaseOrder": "Create your first purchase order to get started", "orderNumber": "Order Number", "supplier": "Supplier", "orderDate": "Order Date", "expectedDate": "Expected Date", "ordered": "Ordered", "received": "Received", "errorLoadingPurchases": "Error loading purchases", "suppliersManagement": "Suppliers Management", "addSupplier": "Add Supplier", "searchSuppliers": "Search suppliers...", "allSuppliers": "All Suppliers", "activeSuppliers": "Active Suppliers", "inactiveSuppliers": "Inactive Suppliers", "noSuppliersFound": "No suppliers found", "addFirstSupplier": "Add your first supplier to get started", "name": "Name", "contactPerson": "Contact Person", "email": "Email", "phone": "Phone", "city": "City", "rating": "Rating", "active": "Active", "errorLoadingSuppliers": "Error loading suppliers", "inventory": "Inventory", "sales": "Sales", "purchases": "Purchases", "reports": "Reports", "settings": "Settings", "logout": "Logout", "lightTheme": "Light Theme", "darkTheme": "Dark Theme", "language": "Language", "arabic": "Arabic", "english": "English", "shared_economy": "Shared Economy", "invalidCredentials": "Invalid username or password.", "usernameRequired": "Username is required.", "passwordRequired": "Password is required.", "ok": "OK", "adminLogin": "<PERSON><PERSON>", "employeeLogin": "Employee Login", "emailRequired": "Email is required.", "invalidEmail": "Please enter a valid email.", "forgotPassword": "Forgot Password?", "forgotPasswordMessage": "A password reset link has been sent to your email.", "createAccount": "Create Account", "createAccountMessage": "Please contact your administrator to create an account.", "companyId": "Company ID", "companyIdRequired": "Company ID is required.", "employeeIdOrEmail": "Employee ID or Email", "employeeIdOrEmailRequired": "Employee ID or Email is required.", "enterEmailForReset": "Enter your email to receive a password reset link.", "sendResetLink": "Send Reset Link", "companyName": "Company Name", "companyNameRequired": "Company Name is required.", "confirmPassword": "Confirm Password", "confirmPasswordRequired": "Confirm Password is required.", "passwordsMismatch": "Passwords do not match.", "accountCreationSuccess": "Account created successfully! Please check your email for verification.", "dashboardMain": "Main Dashboard", "salesSummaryReport": "Sales Summary Report", "inventoryMovementReport": "Inventory Movement Report", "revenueExpensesReport": "Revenue and Expenses Report", "pos": "Point of Sale", "invoices": "Invoices", "createInvoice": "Create New Invoice", "invoiceList": "Invoice List", "taxInvoices": "Tax Invoices", "pendingInvoices": "Pending Invoices", "quotes": "Quotes", "createQuote": "Create Quote", "quoteTracking": "Quote Tracking", "returns": "Returns", "salesReturn": "Sales Return", "salesReturnHistory": "Sales Return History", "pendingApprovals": "Pending Approvals", "customers": "Customers", "addCustomer": "Add New Customer", "customerDirectory": "Customer Directory", "customerAccounts": "Customer Accounts", "customerStatement": "Customer Statement", "vipCustomers": "VIP Customers", "purchaseOrders": "Purchase Orders", "createPurchaseOrder": "Create Purchase Order", "purchaseOrderList": "Purchase Order List", "pendingOrders": "Pending Orders", "deliveryTracking": "Delivery Tracking", "purchaseInvoices": "Purchase Invoices", "recordInvoice": "Record Invoice", "purchaseInvoiceList": "Purchase Invoice List", "payments": "Payments", "suppliers": "Suppliers", "supplierDirectory": "Supplier Directory", "supplierAccounts": "Supplier Accounts", "supplierRating": "Supplier Rating", "autoPurchaseOrders": "Automatic Purchase Orders", "purchaseRules": "Rules and Conditions", "suggestedProducts": "Suggested Products", "purchaseOrderHistory": "Purchase Order History", "productList": "Product List", "oemNumbers": "OEM Numbers", "carModelLinking": "Car Model Linking", "barcodeLabels": "Barcode and Labels", "inventoryLevels": "Inventory Levels", "inventoryMovement": "Inventory Movement", "lowStockAlerts": "Low Stock Alerts", "expiredProducts": "Expired Products", "transfers": "Transfers", "branchTransfer": "Branch Transfer", "transferRequests": "Transfer Requests", "receiveTransfers": "Receive Transfers", "transferHistory": "Transfer History", "inventoryCounting": "Inventory Counting", "periodicInventory": "Periodic Inventory", "surpriseInventory": "Surprise Inventory", "inventoryAdjustment": "Inventory Adjustment", "discrepancyReports": "Discrepancy Reports", "categoriesBrands": "Categories and Brands", "categoryManagement": "Category Management", "brandManagement": "Brand Management", "modelsYearsManagement": "Models and Years Management", "finance": "Finance", "accounts": "Accounts", "chartOfAccounts": "Chart of Accounts", "dailyEntries": "Daily Entries", "trialBalance": "Trial Balance", "cash": "Cash", "cashMovement": "Cash Movement", "depositsWithdrawals": "Deposits and Withdrawals", "cashReconciliation": "Cash Reconciliation", "dailyCashStatement": "Daily Cash Statement", "banks": "Banks", "bankAccounts": "Bank Accounts", "bankTransfers": "Bank Transfers", "bankReconciliation": "Bank Reconciliation", "paymentsReceipts": "Payments and Receipts", "receiptVouchers": "Receipt Vouchers", "paymentVouchers": "Payment Vouchers", "checks": "Checks", "electronicPayments": "Electronic Payments", "expenses": "Expenses", "recordExpense": "Record Expense", "expenseCategories": "Expense Categories", "recurringExpenses": "Recurring Expenses", "hr": "HR", "employees": "Employees", "attendance": "Attendance", "leave": "Leave", "payroll": "Payroll", "users": "Users", "userManagement": "User Management", "permissions": "Permissions", "loginLog": "<PERSON><PERSON>g", "activitiesLog": "Activities Log", "company": "Company", "system": "System", "integrations": "Integrations", "backup": "Backup & Restore", "sync": "Sync", "tools": "Tools", "calculators": "Calculators", "barcode": "Barcode", "barcodeGenerator": "Barcode Generator", "barcodePrinting": "Barcode Printing", "barcodeReader": "Barcode Reader", "importExport": "Import/Export", "communications": "Communications", "messages": "Messages", "smsTemplates": "SMS Templates", "campaigns": "Campaigns", "whatsApp": "WhatsApp", "notifications": "Notifications", "securityMonitoring": "Security & Monitoring", "systemLogs": "System Logs", "auditing": "Auditing", "advancedPermissions": "Advanced Permissions", "help": "Help", "userGuide": "User Guide", "technicalSupport": "Technical Support", "updates": "Updates"}