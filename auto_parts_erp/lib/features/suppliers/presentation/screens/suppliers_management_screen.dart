import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:go_router/go_router.dart';
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/core/database/database_provider.dart';
import 'package:auto_parts_erp/core/database/local/database.dart';

// Suppliers provider
final suppliersProvider = FutureProvider<List<Supplier>>((ref) async {
  final supplierRepo = ref.watch(supplierRepositoryProvider);
  return await supplierRepo.getAllSuppliers();
});

// Search query provider
final suppliersSearchQueryProvider = StateProvider<String>((ref) => '');

// Active filter provider
final suppliersActiveFilterProvider = StateProvider<bool?>((ref) => null);

// Filtered suppliers provider
final filteredSuppliersProvider = Provider<AsyncValue<List<Supplier>>>((ref) {
  final suppliersAsync = ref.watch(suppliersProvider);
  final searchQuery = ref.watch(suppliersSearchQueryProvider);
  final activeFilter = ref.watch(suppliersActiveFilterProvider);
  
  return suppliersAsync.when(
    data: (suppliers) {
      var filtered = suppliers;
      
      // Apply search filter
      if (searchQuery.isNotEmpty) {
        filtered = filtered.where((supplier) =>
          supplier.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          (supplier.email?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
          (supplier.phone?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false)
        ).toList();
      }
      
      // Apply active filter
      if (activeFilter != null) {
        filtered = filtered.where((supplier) => supplier.isActive == activeFilter).toList();
      }
      
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

class SuppliersManagementScreen extends ConsumerStatefulWidget {
  const SuppliersManagementScreen({super.key});

  @override
  ConsumerState<SuppliersManagementScreen> createState() => _SuppliersManagementScreenState();
}

class _SuppliersManagementScreenState extends ConsumerState<SuppliersManagementScreen> {
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filteredSuppliers = ref.watch(filteredSuppliersProvider);
    final activeFilter = ref.watch(suppliersActiveFilterProvider);
    
    return ScaffoldPage(
      header: PageHeader(
        title: Text(AppLocalizations.of(context).translate('suppliersManagement')),
        commandBar: Row(
          children: [
            FilledButton(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.add),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('addSupplier')),
                ],
              ),
              onPressed: () {
                _showAddSupplierDialog();
              },
            ),
            const SizedBox(width: 8),
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.refresh),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('refresh')),
                ],
              ),
              onPressed: () {
                ref.invalidate(suppliersProvider);
              },
            ),
          ],
        ),
      ),
      content: Column(
        children: [
          // Filters Row
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // Search
                Expanded(
                  flex: 2,
                  child: TextBox(
                    controller: _searchController,
                    placeholder: AppLocalizations.of(context).translate('searchSuppliers'),
                    prefix: const Icon(FluentIcons.search),
                    onChanged: (value) {
                      ref.read(suppliersSearchQueryProvider.notifier).state = value;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                
                // Active Filter
                Expanded(
                  child: ComboBox<bool?>(
                    placeholder: Text(AppLocalizations.of(context).translate('allSuppliers')),
                    value: activeFilter,
                    items: [
                      ComboBoxItem<bool?>(
                        value: null,
                        child: Text(AppLocalizations.of(context).translate('allSuppliers')),
                      ),
                      ComboBoxItem<bool?>(
                        value: true,
                        child: Text(AppLocalizations.of(context).translate('activeSuppliers')),
                      ),
                      ComboBoxItem<bool?>(
                        value: false,
                        child: Text(AppLocalizations.of(context).translate('inactiveSuppliers')),
                      ),
                    ],
                    onChanged: (value) {
                      ref.read(suppliersActiveFilterProvider.notifier).state = value;
                    },
                  ),
                ),
              ],
            ),
          ),
          
          // Suppliers Data Grid
          Expanded(
            child: filteredSuppliers.when(
              data: (suppliers) {
                if (suppliers.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(FluentIcons.contact, size: 64),
                        const SizedBox(height: 16),
                        Text(
                          AppLocalizations.of(context).translate('noSuppliersFound'),
                          style: FluentTheme.of(context).typography.subtitle,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          AppLocalizations.of(context).translate('addFirstSupplier'),
                          style: FluentTheme.of(context).typography.body,
                        ),
                      ],
                    ),
                  );
                }
                
                return SfDataGrid(
                  source: SuppliersDataSource(suppliers: suppliers, context: context),
                  columnWidthMode: ColumnWidthMode.fill,
                  allowSorting: true,
                  allowFiltering: true,
                  columns: <GridColumn>[
                    GridColumn(
                      columnName: 'name',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context).translate('name'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'contactPerson',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context).translate('contactPerson'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'email',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context).translate('email'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'phone',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context).translate('phone'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'city',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context).translate('city'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'rating',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.center,
                        child: Text(
                          AppLocalizations.of(context).translate('rating'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'isActive',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.center,
                        child: Text(
                          AppLocalizations.of(context).translate('active'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'actions',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.center,
                        child: Text(
                          AppLocalizations.of(context).translate('actions'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      width: 120,
                    ),
                  ],
                );
              },
              loading: () => const Center(child: ProgressRing()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(FluentIcons.error, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context).translate('errorLoadingSuppliers'),
                      style: FluentTheme.of(context).typography.subtitle,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: FluentTheme.of(context).typography.body,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    FilledButton(
                      child: Text(AppLocalizations.of(context).translate('retry')),
                      onPressed: () {
                        ref.invalidate(suppliersProvider);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddSupplierDialog() {
    showDialog(
      context: context,
      builder: (context) => ContentDialog(
        title: Text(AppLocalizations.of(context).translate('addSupplier')),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Add supplier form will be implemented here.'),
          ],
        ),
        actions: [
          Button(
            child: Text(AppLocalizations.of(context).translate('cancel')),
            onPressed: () => Navigator.of(context).pop(),
          ),
          FilledButton(
            child: Text(AppLocalizations.of(context).translate('save')),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}

// Data source for the suppliers grid
class SuppliersDataSource extends DataGridSource {
  SuppliersDataSource({required List<Supplier> suppliers, required BuildContext context}) {
    _suppliers = suppliers;
    _context = context;
    buildDataGridRows();
  }

  List<Supplier> _suppliers = [];
  List<DataGridRow> _dataGridRows = [];
  late BuildContext _context;

  void buildDataGridRows() {
    _dataGridRows = _suppliers.map<DataGridRow>((supplier) {
      return DataGridRow(cells: [
        DataGridCell<String>(columnName: 'name', value: supplier.name),
        DataGridCell<String>(columnName: 'contactPerson', value: supplier.contactPerson ?? ''),
        DataGridCell<String>(columnName: 'email', value: supplier.email ?? ''),
        DataGridCell<String>(columnName: 'phone', value: supplier.phone ?? ''),
        DataGridCell<String>(columnName: 'city', value: supplier.city ?? ''),
        DataGridCell<double>(columnName: 'rating', value: supplier.rating),
        DataGridCell<bool>(columnName: 'isActive', value: supplier.isActive),
        DataGridCell<Widget>(
          columnName: 'actions',
          value: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(FluentIcons.edit, size: 16),
                onPressed: () {
                  // TODO: Edit supplier
                },
              ),
              IconButton(
                icon: Icon(
                  supplier.isActive ? FluentIcons.blocked_site : FluentIcons.check_mark,
                  size: 16,
                ),
                onPressed: () {
                  // TODO: Toggle supplier active status
                },
              ),
            ],
          ),
        ),
      ]);
    }).toList();
  }

  @override
  List<DataGridRow> get rows => _dataGridRows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((cell) {
        if (cell.columnName == 'actions') {
          return Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(8.0),
            child: cell.value,
          );
        } else if (cell.columnName == 'rating') {
          return Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(FluentIcons.favorite_star_fill, size: 16, color: Colors.orange),
                const SizedBox(width: 4),
                Text(
                  cell.value != null ? cell.value.toStringAsFixed(1) : '0.0',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ],
            ),
          );
        } else if (cell.columnName == 'isActive') {
          return Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(8.0),
            child: Icon(
              cell.value == true ? FluentIcons.check_mark : FluentIcons.blocked_site,
              size: 16,
              color: cell.value == true ? Colors.green : Colors.red,
            ),
          );
        } else {
          return Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(8.0),
            child: Text(cell.value?.toString() ?? ''),
          );
        }
      }).toList(),
    );
  }
}
