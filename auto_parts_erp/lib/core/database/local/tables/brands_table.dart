import 'package:drift/drift.dart';

@DataClassName('Brand')
class Brands extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get nameEn => text().withLength(min: 2, max: 100)();
  TextColumn get nameAr => text().withLength(min: 2, max: 100)();
  TextColumn get descriptionEn => text().nullable()();
  TextColumn get descriptionAr => text().nullable()();
  TextColumn get logoUrl => text().nullable()();
  TextColumn get website => text().nullable()();
  TextColumn get countryOfOrigin => text().nullable()();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().nullable()();
}
