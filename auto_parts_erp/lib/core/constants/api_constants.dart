class ApiConstants {
  // Base URLs
  static const String baseUrl = 'http://localhost:8080/api';
  static const String productionUrl = 'https://api.autopartserp.com/api';
  
  // Auth endpoints
  static const String loginEndpoint = '/auth/login';
  static const String refreshTokenEndpoint = '/auth/refresh';
  static const String logoutEndpoint = '/auth/logout';
  
  // Products endpoints
  static const String productsEndpoint = '/products';
  static const String categoriesEndpoint = '/categories';
  static const String brandsEndpoint = '/brands';
  
  // Inventory endpoints
  static const String inventoryEndpoint = '/inventory';
  static const String inventoryMovementsEndpoint = '/inventory/movements';
  
  // Sales endpoints
  static const String salesEndpoint = '/sales';
  static const String customersEndpoint = '/customers';
  
  // Purchases endpoints
  static const String purchasesEndpoint = '/purchases';
  static const String suppliersEndpoint = '/suppliers';
  
  // Sync endpoints
  static const String syncEndpoint = '/sync';
  static const String syncPullEndpoint = '/sync/pull';
  static const String syncPushEndpoint = '/sync/push';
  
  // Reports endpoints
  static const String reportsEndpoint = '/reports';
  
  // Settings endpoints
  static const String settingsEndpoint = '/settings';
  static const String branchesEndpoint = '/branches';
  static const String usersEndpoint = '/users';
  
  // File upload endpoints
  static const String uploadEndpoint = '/upload';
  static const String imagesEndpoint = '/images';
  
  // WebSocket endpoints
  static const String wsEndpoint = '/ws';
  
  // Timeouts
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Pagination
  static const int defaultPageSize = 50;
  static const int maxPageSize = 100;
  
  // Cache durations
  static const Duration shortCacheDuration = Duration(minutes: 5);
  static const Duration mediumCacheDuration = Duration(minutes: 30);
  static const Duration longCacheDuration = Duration(hours: 2);
}
