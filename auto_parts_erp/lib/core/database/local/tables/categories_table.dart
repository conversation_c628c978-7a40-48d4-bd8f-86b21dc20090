import 'package:drift/drift.dart';

@DataClassName('Category')
class Categories extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get nameEn => text().withLength(min: 2, max: 100)();
  TextColumn get nameAr => text().withLength(min: 2, max: 100)();
  TextColumn get descriptionEn => text().nullable()();
  TextColumn get descriptionAr => text().nullable()();
  IntColumn get parentId => integer().nullable()(); // For subcategories
  TextColumn get iconName => text().nullable()();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().nullable()();
}
