
import 'package:auto_parts_erp/features/auth/presentation/screens/create_account_screen.dart';
import 'package:auto_parts_erp/features/auth/presentation/screens/forgot_password_screen.dart';
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/shared/main_shell.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AdminLoginForm extends StatefulWidget {
  const AdminLoginForm({super.key});

  @override
  State<AdminLoginForm> createState() => _AdminLoginFormState();
}

class _AdminLoginFormState extends State<AdminLoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _rememberMe = false;

  @override
  void initState() {
    super.initState();
    _loadRememberMe();
  }

  _loadRememberMe() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      _rememberMe = prefs.getBool('rememberAdmin') ?? false;
      if (_rememberMe) {
        _emailController.text = prefs.getString('adminEmail') ?? '';
        _passwordController.text = prefs.getString('adminPassword') ?? '';
      }
    });
  }

  _saveRememberMe() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('rememberAdmin', _rememberMe);
    if (_rememberMe) {
      prefs.setString('adminEmail', _emailController.text);
      prefs.setString('adminPassword', _passwordController.text);
    } else {
      prefs.remove('adminEmail');
      prefs.remove('adminPassword');
    }
  }

  void _login() {
    if (_formKey.currentState!.validate()) {
      // Mock login logic
      if (_emailController.text == '<EMAIL>' && _passwordController.text == 'admin') {
        _saveRememberMe();
        Navigator.pushReplacement(context, FluentPageRoute(builder: (context) => const MainShell(child: SizedBox())));
      } else {
        showDialog(
          context: context,
          builder: (context) => ContentDialog(
            title: Text(AppLocalizations.of(context).translate('login')),
            content: Text(AppLocalizations.of(context).translate('invalidCredentials')),
            actions: [
              FilledButton(child: Text(AppLocalizations.of(context).translate('ok')), onPressed: () => Navigator.pop(context)),
            ],
          ),
        );
      }
    }
  }

  void _forgotPassword() {
    showDialog(
      context: context,
      builder: (context) => const ForgotPasswordScreen(),
    );
  }

  void _createAccount() {
    showDialog(
      context: context,
      builder: (context) => const CreateAccountScreen(),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TextFormBox(
            controller: _emailController,
            placeholder: AppLocalizations.of(context).translate('email'),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppLocalizations.of(context).translate('emailRequired');
              }
              // Basic email validation
              if (!value.contains('@')) {
                return AppLocalizations.of(context).translate('invalidEmail');
              }
              return null;
            },
          ),
          const SizedBox(height: 20),
          TextFormBox(
            controller: _passwordController,
            placeholder: AppLocalizations.of(context).translate('password'),
            obscureText: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppLocalizations.of(context).translate('passwordRequired');
              }
              return null;
            },
          ),
          const SizedBox(height: 20),
          Checkbox(
            content: Text(AppLocalizations.of(context).translate('rememberMe')),
            checked: _rememberMe,
            onChanged: (value) {
              setState(() {
                _rememberMe = value!;
              });
            },
          ),
          const SizedBox(height: 20),
          FilledButton(
            onPressed: _login,
            child: Text(AppLocalizations.of(context).translate('login')),
          ),
          const SizedBox(height: 10),
          Button(onPressed: _forgotPassword, child: Text(AppLocalizations.of(context).translate('forgotPassword'))),
          const SizedBox(height: 10),
          Button(onPressed: _createAccount, child: Text(AppLocalizations.of(context).translate('createAccount'))),
        ],
      ),
    );
  }
}
