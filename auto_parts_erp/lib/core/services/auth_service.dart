import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:drift/drift.dart';
import '../database/database_provider.dart';
import '../database/local/database.dart';
import '../network/api_client.dart';
import '../constants/app_constants.dart';

// Auth service provider
final authServiceProvider = Provider<AuthService>((ref) {
  final database = ref.watch(databaseProvider);
  final apiClient = ref.watch(apiClientProvider);
  return AuthService(database, apiClient);
});

// Auth state provider
final authStateProvider = StateNotifierProvider<AuthStateNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthStateNotifier(authService);
});

// Current user provider
final currentUserProvider = StateProvider<User?>((ref) => null);

class AuthService {
  final AppDatabase _database;
  final ApiClient _apiClient;
  
  AuthService(this._database, this._apiClient);
  
  // Login with username and password
  Future<AuthResult> login(String username, String password, {bool rememberMe = false}) async {
    try {
      print('🔐 Attempting login with username: $username, password: $password');

      // Try local authentication first (offline mode)
      final localUser = await _database.getUserByUsername(username);
      print('👤 Local user found: ${localUser?.username}, passwordHash: ${localUser?.passwordHash}');

      if (localUser != null && localUser.passwordHash == password) {
        // Local authentication successful
        print('✅ Local authentication successful');
        await _saveAuthData(localUser, null, null, rememberMe);
        return AuthResult.success(localUser, 'Logged in offline');
      }

      print('❌ Local authentication failed, trying online...');

      // Try online authentication only if local fails
      try {
        final response = await _apiClient.login(username, password);

        if (response.statusCode == 200) {
          final data = jsonDecode(response.data);
          final userData = data['user'];
          final accessToken = data['access_token'];
          final refreshToken = data['refresh_token'];

          // Create or update local user
          final user = await _createOrUpdateLocalUser(userData);

          // Save auth data
          await _saveAuthData(user, accessToken, refreshToken, rememberMe);

          return AuthResult.success(user, 'Logged in successfully');
        } else {
          return AuthResult.error('Invalid credentials');
        }
      } catch (e) {
        print('❌ Online authentication failed: $e');
        return AuthResult.error('Login failed: Invalid credentials');
      }
    } catch (e) {
      print('❌ Login error: $e');
      return AuthResult.error('Login failed: $e');
    }
  }
  
  // Logout
  Future<void> logout() async {
    try {
      // Try to logout from server
      // await _apiClient.logout(); // TODO: Implement logout method
    } catch (e) {
      // Ignore server logout errors
    }
    
    // Clear local auth data
    await _clearAuthData();
  }
  
  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(AppConstants.keyAuthToken);
    final username = prefs.getString(AppConstants.keyLastUsername);
    
    if (username != null) {
      // Check if user exists locally
      final user = await _database.getUserByUsername(username);
      return user != null;
    }
    
    return token != null;
  }
  
  // Get current user
  Future<User?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final username = prefs.getString(AppConstants.keyLastUsername);
    
    if (username != null) {
      return await _database.getUserByUsername(username);
    }
    
    return null;
  }
  
  // Refresh token
  Future<AuthResult> refreshToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final refreshToken = prefs.getString(AppConstants.keyRefreshToken);
      
      if (refreshToken == null) {
        return AuthResult.error('No refresh token available');
      }
      
      final response = await _apiClient.refreshToken(refreshToken);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.data);
        final newAccessToken = data['access_token'];
        
        // Update stored token
        await prefs.setString(AppConstants.keyAuthToken, newAccessToken);
        
        return AuthResult.success(null, 'Token refreshed');
      } else {
        return AuthResult.error('Token refresh failed');
      }
    } catch (e) {
      return AuthResult.error('Token refresh failed: $e');
    }
  }
  
  // Auto login (restore session)
  Future<AuthResult> autoLogin() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rememberMe = prefs.getBool(AppConstants.keyRememberMe) ?? false;
      
      if (!rememberMe) {
        return AuthResult.error('Auto login disabled');
      }
      
      final username = prefs.getString(AppConstants.keyLastUsername);
      if (username == null) {
        return AuthResult.error('No saved username');
      }
      
      final user = await _database.getUserByUsername(username);
      if (user == null) {
        return AuthResult.error('User not found');
      }
      
      // Try to refresh token if available
      final token = prefs.getString(AppConstants.keyAuthToken);
      if (token != null) {
        final refreshResult = await refreshToken();
        if (refreshResult.success) {
          return AuthResult.success(user, 'Session restored');
        }
      }
      
      // Fallback to offline mode
      return AuthResult.success(user, 'Logged in offline');
    } catch (e) {
      return AuthResult.error('Auto login failed: $e');
    }
  }
  
  // Change password
  Future<AuthResult> changePassword(String currentPassword, String newPassword) async {
    try {
      final user = await getCurrentUser();
      if (user == null) {
        return AuthResult.error('No user logged in');
      }
      
      // Verify current password
      if (user.passwordHash != currentPassword) {
        return AuthResult.error('Current password is incorrect');
      }
      
      // Update password locally
      await (_database.update(_database.users)
        ..where((u) => u.id.equals(user.id)))
        .write(UsersCompanion(passwordHash: Value(newPassword)));
      
      // Try to update on server
      try {
        // TODO: Implement server password change API
        // await _apiClient.changePassword(currentPassword, newPassword);
      } catch (e) {
        // Queue for sync later
        // TODO: Add to sync queue
      }
      
      return AuthResult.success(null, 'Password changed successfully');
    } catch (e) {
      return AuthResult.error('Password change failed: $e');
    }
  }
  
  // Helper methods
  Future<User> _createOrUpdateLocalUser(Map<String, dynamic> userData) async {
    final existingUser = await _database.getUserByUsername(userData['username']);
    
    if (existingUser != null) {
      // Update existing user
      await (_database.update(_database.users)
        ..where((u) => u.id.equals(existingUser.id)))
        .write(UsersCompanion(
          email: Value(userData['email']),
          role: Value(userData['role']),
          branchId: Value(userData['branch_id']),
          updatedAt: Value(DateTime.now()),
        ));
      
      return await _database.getUserByUsername(userData['username']) ?? existingUser;
    } else {
      // Create new user
      final userId = await _database.into(_database.users).insert(
        UsersCompanion.insert(
          username: userData['username'],
          email: Value(userData['email']),
          passwordHash: 'synced', // Placeholder for synced users
          role: userData['role'] ?? 'employee',
          branchId: Value(userData['branch_id']),
        ),
      );
      
      final user = await _database.getUserByUsername(userData['username']);
      return user!;
    }
  }
  
  Future<void> _saveAuthData(User user, String? accessToken, String? refreshToken, bool rememberMe) async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setString(AppConstants.keyLastUsername, user.username);
    await prefs.setBool(AppConstants.keyRememberMe, rememberMe);
    await prefs.setInt(AppConstants.keyCurrentBranch, user.branchId ?? 1);
    
    if (accessToken != null) {
      await prefs.setString(AppConstants.keyAuthToken, accessToken);
    }
    
    if (refreshToken != null) {
      await prefs.setString(AppConstants.keyRefreshToken, refreshToken);
    }
    
    // Update last login time
    await (_database.update(_database.users)
      ..where((u) => u.id.equals(user.id)))
      .write(UsersCompanion(lastLoginAt: Value(DateTime.now())));
  }
  
  Future<void> _clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.remove(AppConstants.keyAuthToken);
    await prefs.remove(AppConstants.keyRefreshToken);
    await prefs.remove(AppConstants.keyLastUsername);
    await prefs.setBool(AppConstants.keyRememberMe, false);
  }
}

// Auth state classes
class AuthState {
  final bool isLoading;
  final User? user;
  final String? message;
  final bool isLoggedIn;
  
  const AuthState({
    this.isLoading = false,
    this.user,
    this.message,
    this.isLoggedIn = false,
  });
  
  AuthState copyWith({
    bool? isLoading,
    User? user,
    String? message,
    bool? isLoggedIn,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      message: message ?? this.message,
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
    );
  }
}

class AuthStateNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;
  
  AuthStateNotifier(this._authService) : super(const AuthState()) {
    _checkAuthStatus();
  }
  
  Future<void> _checkAuthStatus() async {
    final isLoggedIn = await _authService.isLoggedIn();
    if (isLoggedIn) {
      final user = await _authService.getCurrentUser();
      state = state.copyWith(user: user, isLoggedIn: true);
    }
  }
  
  Future<void> login(String username, String password, {bool rememberMe = false}) async {
    print('🔔 AuthStateNotifier: login called with username: $username, password: $password');
    state = state.copyWith(isLoading: true, message: null);

    final result = await _authService.login(username, password, rememberMe: rememberMe);
    
    state = state.copyWith(
      isLoading: false,
      user: result.user,
      message: result.message,
      isLoggedIn: result.success,
    );
  }
  
  Future<void> logout() async {
    state = state.copyWith(isLoading: true);
    
    await _authService.logout();
    
    state = const AuthState(message: 'Logged out successfully');
  }
  
  Future<void> autoLogin() async {
    state = state.copyWith(isLoading: true);
    
    final result = await _authService.autoLogin();
    
    state = state.copyWith(
      isLoading: false,
      user: result.user,
      message: result.message,
      isLoggedIn: result.success,
    );
  }
  
  void clearMessage() {
    state = state.copyWith(message: null);
  }
}

class AuthResult {
  final bool success;
  final User? user;
  final String message;
  
  const AuthResult.success(this.user, this.message) : success = true;
  const AuthResult.error(this.message) : success = false, user = null;
}
