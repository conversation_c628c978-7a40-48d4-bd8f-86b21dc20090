import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:go_router/go_router.dart';
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/core/database/database_provider.dart';
import 'package:auto_parts_erp/core/database/local/database.dart';

// Products provider
final productsProvider = FutureProvider<List<Product>>((ref) async {
  final productRepo = ref.watch(productRepositoryProvider);
  return await productRepo.getAllProducts();
});

// Search query provider
final searchQueryProvider = StateProvider<String>((ref) => '');

// Filtered products provider
final filteredProductsProvider = Provider<AsyncValue<List<Product>>>((ref) {
  final productsAsync = ref.watch(productsProvider);
  final searchQuery = ref.watch(searchQueryProvider);
  
  return productsAsync.when(
    data: (products) {
      if (searchQuery.isEmpty) {
        return AsyncValue.data(products);
      }
      final filtered = products.where((product) =>
        product.nameEn.toLowerCase().contains(searchQuery.toLowerCase()) ||
        product.nameAr.toLowerCase().contains(searchQuery.toLowerCase()) ||
        (product.sku?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false)
      ).toList();
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

class ProductListScreen extends ConsumerStatefulWidget {
  const ProductListScreen({super.key});

  @override
  ConsumerState<ProductListScreen> createState() => _ProductListScreenState();
}

class _ProductListScreenState extends ConsumerState<ProductListScreen> {
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filteredProducts = ref.watch(filteredProductsProvider);
    
    return ScaffoldPage(
      header: PageHeader(
        title: Text(AppLocalizations.of(context).translate('products')),
      ),
      content: Column(
        children: [
          // Search and Add Product Row
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: TextBox(
                    controller: _searchController,
                    placeholder: AppLocalizations.of(context).translate('searchProducts'),
                    prefix: const Icon(FluentIcons.search),
                    onChanged: (value) {
                      ref.read(searchQueryProvider.notifier).state = value;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                FilledButton(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(FluentIcons.add),
                      const SizedBox(width: 8),
                      Text(AppLocalizations.of(context).translate('addProduct')),
                    ],
                  ),
                  onPressed: () {
                    context.go('/products/add');
                  },
                ),
              ],
            ),
          ),
          
          // Products Data Grid
          Expanded(
            child: filteredProducts.when(
              data: (products) {
                if (products.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(FluentIcons.product_variant, size: 64),
                        const SizedBox(height: 16),
                        Text(
                          AppLocalizations.of(context).translate('noProductsFound'),
                          style: FluentTheme.of(context).typography.subtitle,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          AppLocalizations.of(context).translate('addFirstProduct'),
                          style: FluentTheme.of(context).typography.body,
                        ),
                      ],
                    ),
                  );
                }
                
                return SfDataGrid(
                  source: ProductDataSource(products: products),
                  columnWidthMode: ColumnWidthMode.fill,
                  allowSorting: true,
                  allowFiltering: true,
                  columns: <GridColumn>[
                    GridColumn(
                      columnName: 'nameEn',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context).translate('nameEn'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'nameAr',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context).translate('nameAr'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'sku',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context).translate('sku'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'sellingPrice',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerRight,
                        child: Text(
                          AppLocalizations.of(context).translate('price'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'actions',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.center,
                        child: Text(
                          AppLocalizations.of(context).translate('actions'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      width: 120,
                    ),
                  ],
                );
              },
              loading: () => const Center(child: ProgressRing()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(FluentIcons.error, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context).translate('errorLoadingProducts'),
                      style: FluentTheme.of(context).typography.subtitle,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: FluentTheme.of(context).typography.body,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    FilledButton(
                      child: Text(AppLocalizations.of(context).translate('retry')),
                      onPressed: () {
                        ref.invalidate(productsProvider);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Data source for the products grid
class ProductDataSource extends DataGridSource {
  ProductDataSource({required List<Product> products}) {
    _products = products;
    buildDataGridRows();
  }

  List<Product> _products = [];
  List<DataGridRow> _dataGridRows = [];

  void buildDataGridRows() {
    _dataGridRows = _products.map<DataGridRow>((product) {
      return DataGridRow(cells: [
        DataGridCell<String>(columnName: 'nameEn', value: product.nameEn),
        DataGridCell<String>(columnName: 'nameAr', value: product.nameAr),
        DataGridCell<String>(columnName: 'sku', value: product.sku ?? ''),
        DataGridCell<double>(columnName: 'sellingPrice', value: product.sellingPrice),
        DataGridCell<Widget>(
          columnName: 'actions',
          value: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(FluentIcons.edit, size: 16),
                onPressed: () {
                  // TODO: Navigate to edit product
                },
              ),
              IconButton(
                icon: const Icon(FluentIcons.delete, size: 16),
                onPressed: () {
                  // TODO: Delete product
                },
              ),
            ],
          ),
        ),
      ]);
    }).toList();
  }

  @override
  List<DataGridRow> get rows => _dataGridRows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((cell) {
        if (cell.columnName == 'actions') {
          return Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(8.0),
            child: cell.value,
          );
        } else if (cell.columnName == 'sellingPrice') {
          return Container(
            alignment: Alignment.centerRight,
            padding: const EdgeInsets.all(8.0),
            child: Text(
              cell.value != null ? '\$${cell.value.toStringAsFixed(2)}' : '',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          );
        } else {
          return Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(8.0),
            child: Text(cell.value?.toString() ?? ''),
          );
        }
      }).toList(),
    );
  }
}
