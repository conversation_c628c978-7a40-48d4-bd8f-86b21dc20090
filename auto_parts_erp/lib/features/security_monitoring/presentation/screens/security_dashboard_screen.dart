import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/core/services/security_monitoring_service.dart';

class SecurityDashboardScreen extends ConsumerWidget {
  const SecurityDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final securityStats = ref.watch(securityStatsProvider);
    final securityAlerts = ref.watch(securityAlertsProvider);

    return ScaffoldPage(
      header: PageHeader(
        title: Text(AppLocalizations.of(context).translate('securityDashboard')),
        commandBar: Row(
          children: [
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.shield),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('securitySettings')),
                ],
              ),
              onPressed: () {
                // TODO: Navigate to security settings
              },
            ),
            const SizedBox(width: 8),
            But<PERSON>(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.view_list),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('auditLogs')),
                ],
              ),
              onPressed: () {
                // TODO: Navigate to audit logs
              },
            ),
            const SizedBox(width: 8),
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.refresh),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('refresh')),
                ],
              ),
              onPressed: () {
                ref.invalidate(securityStatsProvider);
              },
            ),
          ],
        ),
      ),
      content: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Security Stats Cards
            securityStats.when(
              data: (stats) => Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      context,
                      AppLocalizations.of(context).translate('securityScore'),
                      '${stats.securityScore.toStringAsFixed(0)}%',
                      FluentIcons.shield,
                      _getSecurityScoreColor(stats.securityScore),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      AppLocalizations.of(context).translate('alertsToday'),
                      stats.alertsToday.toString(),
                      FluentIcons.warning,
                      stats.alertsToday > 5 ? Colors.red : Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      AppLocalizations.of(context).translate('failedLoginsToday'),
                      stats.failedLoginsToday.toString(),
                      FluentIcons.blocked_site,
                      stats.failedLoginsToday > 10 ? Colors.red : Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      AppLocalizations.of(context).translate('activeUsersToday'),
                      stats.activeUsersToday.toString(),
                      FluentIcons.people,
                      Colors.blue,
                    ),
                  ),
                ],
              ),
              loading: () => const Center(child: ProgressRing()),
              error: (error, stack) => Center(
                child: Text('Error loading security stats: $error'),
              ),
            ),
            const SizedBox(height: 24),

            // Security Alerts and Threat Analysis
            Row(
              children: [
                // Active Security Alerts
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(FluentIcons.warning, size: 24, color: Colors.red),
                              const SizedBox(width: 8),
                              Text(
                                AppLocalizations.of(context).translate('activeAlerts'),
                                style: FluentTheme.of(context).typography.subtitle,
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 300,
                            child: securityAlerts.when(
                              data: (alerts) {
                                if (alerts.isEmpty) {
                                  return Center(
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        const Icon(FluentIcons.check_mark, size: 48, color: Colors.green),
                                        const SizedBox(height: 16),
                                        Text(
                                          AppLocalizations.of(context).translate('noActiveAlerts'),
                                          style: FluentTheme.of(context).typography.body,
                                        ),
                                      ],
                                    ),
                                  );
                                }

                                return ListView.builder(
                                  itemCount: alerts.length,
                                  itemBuilder: (context, index) {
                                    final alert = alerts[index];
                                    return _buildAlertItem(context, ref, alert);
                                  },
                                );
                              },
                              loading: () => const Center(child: ProgressRing()),
                              error: (error, stack) => Center(
                                child: Text('Error loading alerts: $error'),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Threat Analysis Chart
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).translate('threatAnalysis'),
                            style: FluentTheme.of(context).typography.subtitle,
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 300,
                            child: PieChart(
                              PieChartData(
                                sections: [
                                  PieChartSectionData(
                                    value: 40,
                                    title: '40%',
                                    color: Colors.green,
                                    radius: 80,
                                  ),
                                  PieChartSectionData(
                                    value: 30,
                                    title: '30%',
                                    color: Colors.orange,
                                    radius: 80,
                                  ),
                                  PieChartSectionData(
                                    value: 20,
                                    title: '20%',
                                    color: Colors.red,
                                    radius: 80,
                                  ),
                                  PieChartSectionData(
                                    value: 10,
                                    title: '10%',
                                    color: Colors.purple,
                                    radius: 80,
                                  ),
                                ],
                                centerSpaceRadius: 40,
                                sectionsSpace: 2,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(BuildContext context, String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 24, color: color),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: FluentTheme.of(context).typography.body,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: FluentTheme.of(context).typography.title?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertItem(BuildContext context, WidgetRef ref, SecurityAlert alert) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        border: Border.all(color: _getSeverityColor(alert.severity).withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: _getSeverityColor(alert.severity).withValues(alpha: 0.05),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getAlertIcon(alert.type),
                size: 20,
                color: _getSeverityColor(alert.severity),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  alert.title,
                  style: FluentTheme.of(context).typography.body?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getSeverityColor(alert.severity).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _getSeverityColor(alert.severity).withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  alert.severity.toString().split('.').last.toUpperCase(),
                  style: TextStyle(
                    color: _getSeverityColor(alert.severity),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            alert.description,
            style: FluentTheme.of(context).typography.body,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                _formatDateTime(alert.timestamp),
                style: FluentTheme.of(context).typography.caption,
              ),
              const Spacer(),
              if (!alert.isAcknowledged) ...[
                Button(
                  child: Text(AppLocalizations.of(context).translate('acknowledge')),
                  onPressed: () {
                    ref.read(securityMonitoringServiceProvider).acknowledgeAlert(
                      alert.id,
                      'Current User', // TODO: Get current user
                    );
                  },
                ),
                const SizedBox(width: 8),
              ],
              Button(
                child: Text(AppLocalizations.of(context).translate('dismiss')),
                onPressed: () {
                  ref.read(securityMonitoringServiceProvider).dismissAlert(alert.id);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getSecurityScoreColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }

  Color _getSeverityColor(SecuritySeverity severity) {
    switch (severity) {
      case SecuritySeverity.low:
        return Colors.blue;
      case SecuritySeverity.medium:
        return Colors.orange;
      case SecuritySeverity.high:
        return Colors.red;
      case SecuritySeverity.critical:
        return const Color(0xFF8B0000); // Dark red
    }
  }

  IconData _getAlertIcon(SecurityAlertType type) {
    switch (type) {
      case SecurityAlertType.suspiciousLogin:
        return FluentIcons.signin;
      case SecurityAlertType.rapidLogins:
        return FluentIcons.speed_high;
      case SecurityAlertType.unusualDataAccess:
        return FluentIcons.database_view;
      case SecurityAlertType.afterHoursAccess:
        return FluentIcons.clock;
      case SecurityAlertType.bruteForceAttempt:
        return FluentIcons.blocked_site;
      case SecurityAlertType.privilegeEscalation:
        return FluentIcons.permissions;
      case SecurityAlertType.largeDataExport:
        return FluentIcons.export;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
