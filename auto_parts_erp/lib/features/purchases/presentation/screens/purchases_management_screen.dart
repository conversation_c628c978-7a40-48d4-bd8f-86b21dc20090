import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:go_router/go_router.dart';
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/core/database/database_provider.dart';
import 'package:auto_parts_erp/core/database/local/database.dart';

// Purchases provider
final purchasesProvider = FutureProvider<List<PurchaseWithDetails>>((ref) async {
  final database = ref.watch(databaseProvider);
  return await database.getPurchasesWithDetails();
});

// Search query provider
final purchasesSearchQueryProvider = StateProvider<String>((ref) => '');

// Date filter provider
final purchasesDateFilterProvider = StateProvider<DateTimeRange?>((ref) => null);

// Status filter provider
final purchasesStatusFilterProvider = StateProvider<String?>((ref) => null);

// Filtered purchases provider
final filteredPurchasesProvider = Provider<AsyncValue<List<PurchaseWithDetails>>>((ref) {
  final purchasesAsync = ref.watch(purchasesProvider);
  final searchQuery = ref.watch(purchasesSearchQueryProvider);
  final dateFilter = ref.watch(purchasesDateFilterProvider);
  final statusFilter = ref.watch(purchasesStatusFilterProvider);
  
  return purchasesAsync.when(
    data: (purchases) {
      var filtered = purchases;
      
      // Apply search filter
      if (searchQuery.isNotEmpty) {
        filtered = filtered.where((purchase) =>
          purchase.purchase.orderNumber.toLowerCase().contains(searchQuery.toLowerCase()) ||
          (purchase.supplier?.name.toLowerCase().contains(searchQuery.toLowerCase()) ?? false)
        ).toList();
      }
      
      // Apply date filter
      if (dateFilter != null) {
        filtered = filtered.where((purchase) =>
          purchase.purchase.orderDate.isAfter(dateFilter.start.subtract(const Duration(days: 1))) &&
          purchase.purchase.orderDate.isBefore(dateFilter.end.add(const Duration(days: 1)))
        ).toList();
      }
      
      // Apply status filter
      if (statusFilter != null && statusFilter.isNotEmpty) {
        filtered = filtered.where((purchase) => purchase.purchase.status == statusFilter).toList();
      }
      
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

class PurchasesManagementScreen extends ConsumerStatefulWidget {
  const PurchasesManagementScreen({super.key});

  @override
  ConsumerState<PurchasesManagementScreen> createState() => _PurchasesManagementScreenState();
}

class _PurchasesManagementScreenState extends ConsumerState<PurchasesManagementScreen> {
  final _searchController = TextEditingController();

  final List<String> _statusOptions = [
    'PENDING',
    'ORDERED',
    'RECEIVED',
    'CANCELLED',
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filteredPurchases = ref.watch(filteredPurchasesProvider);
    final statusFilter = ref.watch(purchasesStatusFilterProvider);
    
    return ScaffoldPage(
      header: PageHeader(
        title: Text(AppLocalizations.of(context).translate('purchasesManagement')),
        commandBar: Row(
          children: [
            FilledButton(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.add),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('newPurchaseOrder')),
                ],
              ),
              onPressed: () {
                context.go('/purchases/new');
              },
            ),
            const SizedBox(width: 8),
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.refresh),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('refresh')),
                ],
              ),
              onPressed: () {
                ref.invalidate(purchasesProvider);
              },
            ),
          ],
        ),
      ),
      content: Column(
        children: [
          // Filters Row
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // Search
                Expanded(
                  flex: 2,
                  child: TextBox(
                    controller: _searchController,
                    placeholder: AppLocalizations.of(context).translate('searchPurchases'),
                    prefix: const Icon(FluentIcons.search),
                    onChanged: (value) {
                      ref.read(purchasesSearchQueryProvider.notifier).state = value;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                
                // Status Filter
                Expanded(
                  child: ComboBox<String>(
                    placeholder: Text(AppLocalizations.of(context).translate('allStatuses')),
                    value: statusFilter,
                    items: [
                      ComboBoxItem<String>(
                        value: null,
                        child: Text(AppLocalizations.of(context).translate('allStatuses')),
                      ),
                      ..._statusOptions.map((status) => ComboBoxItem<String>(
                        value: status,
                        child: Text(_getStatusDisplayName(status)),
                      )),
                    ],
                    onChanged: (status) {
                      ref.read(purchasesStatusFilterProvider.notifier).state = status;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                
                // Date Filter
                Button(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(FluentIcons.calendar),
                      const SizedBox(width: 8),
                      Text(AppLocalizations.of(context).translate('dateFilter')),
                    ],
                  ),
                  onPressed: () {
                    _showDateFilterDialog();
                  },
                ),
              ],
            ),
          ),
          
          // Purchases Data Grid
          Expanded(
            child: filteredPurchases.when(
              data: (purchases) {
                if (purchases.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(FluentIcons.package, size: 64),
                        const SizedBox(height: 16),
                        Text(
                          AppLocalizations.of(context).translate('noPurchasesFound'),
                          style: FluentTheme.of(context).typography.subtitle,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          AppLocalizations.of(context).translate('createFirstPurchaseOrder'),
                          style: FluentTheme.of(context).typography.body,
                        ),
                      ],
                    ),
                  );
                }
                
                return SfDataGrid(
                  source: PurchasesDataSource(purchases: purchases, context: context),
                  columnWidthMode: ColumnWidthMode.fill,
                  allowSorting: true,
                  allowFiltering: true,
                  columns: <GridColumn>[
                    GridColumn(
                      columnName: 'orderNumber',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context).translate('orderNumber'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'supplier',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context).translate('supplier'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'orderDate',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context).translate('orderDate'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'expectedDate',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context).translate('expectedDate'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'totalAmount',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.centerRight,
                        child: Text(
                          AppLocalizations.of(context).translate('total'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'status',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.center,
                        child: Text(
                          AppLocalizations.of(context).translate('status'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    GridColumn(
                      columnName: 'actions',
                      label: Container(
                        padding: const EdgeInsets.all(8.0),
                        alignment: Alignment.center,
                        child: Text(
                          AppLocalizations.of(context).translate('actions'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      width: 150,
                    ),
                  ],
                );
              },
              loading: () => const Center(child: ProgressRing()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(FluentIcons.error, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context).translate('errorLoadingPurchases'),
                      style: FluentTheme.of(context).typography.subtitle,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: FluentTheme.of(context).typography.body,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    FilledButton(
                      child: Text(AppLocalizations.of(context).translate('retry')),
                      onPressed: () {
                        ref.invalidate(purchasesProvider);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'PENDING':
        return AppLocalizations.of(context).translate('pending');
      case 'ORDERED':
        return AppLocalizations.of(context).translate('ordered');
      case 'RECEIVED':
        return AppLocalizations.of(context).translate('received');
      case 'CANCELLED':
        return AppLocalizations.of(context).translate('cancelled');
      default:
        return status;
    }
  }

  void _showDateFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => ContentDialog(
        title: Text(AppLocalizations.of(context).translate('selectDateRange')),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Date range picker will be implemented here.'),
          ],
        ),
        actions: [
          Button(
            child: Text(AppLocalizations.of(context).translate('cancel')),
            onPressed: () => Navigator.of(context).pop(),
          ),
          FilledButton(
            child: Text(AppLocalizations.of(context).translate('apply')),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}

// Data source for the purchases grid
class PurchasesDataSource extends DataGridSource {
  PurchasesDataSource({required List<PurchaseWithDetails> purchases, required BuildContext context}) {
    _purchases = purchases;
    _context = context;
    buildDataGridRows();
  }

  List<PurchaseWithDetails> _purchases = [];
  List<DataGridRow> _dataGridRows = [];
  late BuildContext _context;

  void buildDataGridRows() {
    _dataGridRows = _purchases.map<DataGridRow>((purchaseWithDetails) {
      final purchase = purchaseWithDetails.purchase;
      
      return DataGridRow(cells: [
        DataGridCell<String>(columnName: 'orderNumber', value: purchase.orderNumber),
        DataGridCell<String>(columnName: 'supplier', value: purchaseWithDetails.supplier?.name ?? 'Unknown Supplier'),
        DataGridCell<DateTime>(columnName: 'orderDate', value: purchase.orderDate),
        DataGridCell<DateTime?>(columnName: 'expectedDate', value: purchase.expectedDeliveryDate),
        DataGridCell<double>(columnName: 'totalAmount', value: purchase.totalAmount),
        DataGridCell<String>(columnName: 'status', value: purchase.status),
        DataGridCell<Widget>(
          columnName: 'actions',
          value: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(FluentIcons.view, size: 16),
                onPressed: () {
                  // TODO: View purchase details
                },
              ),
              IconButton(
                icon: const Icon(FluentIcons.edit, size: 16),
                onPressed: () {
                  // TODO: Edit purchase order
                },
              ),
              if (purchase.status == 'ORDERED')
                IconButton(
                  icon: const Icon(FluentIcons.package_delivered, size: 16),
                  onPressed: () {
                    // TODO: Mark as received
                  },
                ),
            ],
          ),
        ),
      ]);
    }).toList();
  }

  @override
  List<DataGridRow> get rows => _dataGridRows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((cell) {
        if (cell.columnName == 'actions') {
          return Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(8.0),
            child: cell.value,
          );
        } else if (cell.columnName == 'totalAmount') {
          return Container(
            alignment: Alignment.centerRight,
            padding: const EdgeInsets.all(8.0),
            child: Text(
              cell.value != null ? '\$${cell.value.toStringAsFixed(2)}' : '\$0.00',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          );
        } else if (cell.columnName == 'orderDate' || cell.columnName == 'expectedDate') {
          return Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(8.0),
            child: Text(
              cell.value != null 
                  ? '${cell.value.day}/${cell.value.month}/${cell.value.year}'
                  : '',
            ),
          );
        } else if (cell.columnName == 'status') {
          return Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(8.0),
            child: _buildStatusChip(cell.value?.toString() ?? ''),
          );
        } else {
          return Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(8.0),
            child: Text(cell.value?.toString() ?? ''),
          );
        }
      }).toList(),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    switch (status) {
      case 'RECEIVED':
        color = Colors.green;
        break;
      case 'ORDERED':
        color = Colors.blue;
        break;
      case 'PENDING':
        color = Colors.orange;
        break;
      case 'CANCELLED':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
