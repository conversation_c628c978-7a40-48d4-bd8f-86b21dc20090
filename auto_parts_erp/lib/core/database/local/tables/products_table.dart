import 'package:drift/drift.dart';

@DataClassName('Product')
class Products extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get nameEn => text().withLength(min: 2, max: 200)();
  TextColumn get nameAr => text().withLength(min: 2, max: 200)();
  TextColumn get descriptionEn => text().nullable()();
  TextColumn get descriptionAr => text().nullable()();
  TextColumn get sku => text().unique().nullable()();
  TextColumn get barcode => text().nullable()();
  IntColumn get categoryId => integer().nullable()();
  IntColumn get brandId => integer().nullable()();
  RealColumn get costPrice => real().withDefault(const Constant(0.0))();
  RealColumn get sellingPrice => real().withDefault(const Constant(0.0))();
  RealColumn get weight => real().nullable()();
  TextColumn get unit => text().withDefault(const Constant('piece'))(); // piece, kg, liter, etc.
  IntColumn get minStockLevel => integer().withDefault(const Constant(0))();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  BoolColumn get hasExpiryDate => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().nullable()();
}

@DataClassName('ProductOemNumber')
class ProductOemNumbers extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get productId => integer()();
  TextColumn get oemNumber => text().withLength(min: 1, max: 100)();
  TextColumn get manufacturer => text().nullable()();
  BoolColumn get isPrimary => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
}

@DataClassName('ProductCarCompatibility')
class ProductCarCompatibility extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get productId => integer()();
  TextColumn get carMake => text().withLength(min: 1, max: 50)();
  TextColumn get carModel => text().withLength(min: 1, max: 50)();
  IntColumn get yearFrom => integer()();
  IntColumn get yearTo => integer().nullable()();
  TextColumn get engineType => text().nullable()();
  TextColumn get notes => text().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
}
