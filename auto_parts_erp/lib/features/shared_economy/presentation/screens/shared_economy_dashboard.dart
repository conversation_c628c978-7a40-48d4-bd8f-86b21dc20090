import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/core/database/database_provider.dart';
import 'package:auto_parts_erp/core/database/local/database.dart';

// Shared economy data providers
final sharedEconomyStatsProvider = FutureProvider<SharedEconomyStats>((ref) async {
  final database = ref.watch(databaseProvider);
  // Mock data for now - replace with actual database calls
  return SharedEconomyStats(
    totalPartners: 25,
    activeListings: 150,
    totalTransactions: 1250,
    monthlyRevenue: 15000.0,
    partnerSatisfaction: 4.5,
    averageResponseTime: 2.5,
  );
});

final recentActivitiesProvider = FutureProvider<List<SharedEconomyActivity>>((ref) async {
  // Mock data for now
  return [
    SharedEconomyActivity(
      id: 1,
      type: 'NEW_PARTNER',
      description: 'New partner joined: Ahmed Auto Parts',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      status: 'COMPLETED',
    ),
    SharedEconomyActivity(
      id: 2,
      type: 'LISTING_CREATED',
      description: 'New listing: BMW Engine Oil Filter',
      timestamp: DateTime.now().subtract(const Duration(hours: 5)),
      status: 'ACTIVE',
    ),
    SharedEconomyActivity(
      id: 3,
      type: 'TRANSACTION',
      description: 'Sale completed: Mercedes Brake Pads',
      timestamp: DateTime.now().subtract(const Duration(hours: 8)),
      status: 'COMPLETED',
    ),
  ];
});

// Data models
class SharedEconomyStats {
  final int totalPartners;
  final int activeListings;
  final int totalTransactions;
  final double monthlyRevenue;
  final double partnerSatisfaction;
  final double averageResponseTime;

  SharedEconomyStats({
    required this.totalPartners,
    required this.activeListings,
    required this.totalTransactions,
    required this.monthlyRevenue,
    required this.partnerSatisfaction,
    required this.averageResponseTime,
  });
}

class SharedEconomyActivity {
  final int id;
  final String type;
  final String description;
  final DateTime timestamp;
  final String status;

  SharedEconomyActivity({
    required this.id,
    required this.type,
    required this.description,
    required this.timestamp,
    required this.status,
  });
}

class SharedEconomyDashboard extends ConsumerWidget {
  const SharedEconomyDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stats = ref.watch(sharedEconomyStatsProvider);
    final activities = ref.watch(recentActivitiesProvider);

    return ScaffoldPage(
      header: PageHeader(
        title: Text(AppLocalizations.of(context).translate('sharedEconomyDashboard')),
        commandBar: Row(
          children: [
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.add_friend),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('addPartner')),
                ],
              ),
              onPressed: () {
                _showAddPartnerDialog(context);
              },
            ),
            const SizedBox(width: 8),
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.product_list),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('manageListings')),
                ],
              ),
              onPressed: () {
                // TODO: Navigate to listings management
              },
            ),
            const SizedBox(width: 8),
            Button(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.refresh),
                  const SizedBox(width: 8),
                  Text(AppLocalizations.of(context).translate('refresh')),
                ],
              ),
              onPressed: () {
                ref.invalidate(sharedEconomyStatsProvider);
                ref.invalidate(recentActivitiesProvider);
              },
            ),
          ],
        ),
      ),
      content: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Stats Cards Row
            stats.when(
              data: (data) => Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      context,
                      AppLocalizations.of(context).translate('totalPartners'),
                      data.totalPartners.toString(),
                      FluentIcons.people,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      AppLocalizations.of(context).translate('activeListings'),
                      data.activeListings.toString(),
                      FluentIcons.product_list,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      AppLocalizations.of(context).translate('totalTransactions'),
                      data.totalTransactions.toString(),
                      FluentIcons.money,
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      AppLocalizations.of(context).translate('monthlyRevenue'),
                      '\$${data.monthlyRevenue.toStringAsFixed(0)}',
                      FluentIcons.chart_series,
                      Colors.purple,
                    ),
                  ),
                ],
              ),
              loading: () => const Center(child: ProgressRing()),
              error: (error, stack) => Center(
                child: Text('Error loading stats: $error'),
              ),
            ),
            const SizedBox(height: 24),

            // Performance Metrics Row
            stats.when(
              data: (data) => Row(
                children: [
                  Expanded(
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context).translate('partnerSatisfaction'),
                              style: FluentTheme.of(context).typography.subtitle,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: LinearProgressIndicator(
                                    value: data.partnerSatisfaction / 5.0,
                                    backgroundColor: Colors.grey.withValues(alpha: 0.3),
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      data.partnerSatisfaction >= 4.0 ? Colors.green : Colors.orange,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Text(
                                  '${data.partnerSatisfaction}/5.0',
                                  style: FluentTheme.of(context).typography.body?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context).translate('averageResponseTime'),
                              style: FluentTheme.of(context).typography.subtitle,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Icon(
                                  FluentIcons.clock,
                                  size: 24,
                                  color: data.averageResponseTime <= 3.0 ? Colors.green : Colors.orange,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '${data.averageResponseTime} hours',
                                  style: FluentTheme.of(context).typography.body?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: data.averageResponseTime <= 3.0 ? Colors.green : Colors.orange,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              loading: () => const Center(child: ProgressRing()),
              error: (error, stack) => const SizedBox(),
            ),
            const SizedBox(height: 24),

            // Recent Activities and Revenue Chart
            Row(
              children: [
                // Recent Activities
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).translate('recentActivities'),
                            style: FluentTheme.of(context).typography.subtitle,
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 300,
                            child: activities.when(
                              data: (activitiesList) => ListView.builder(
                                itemCount: activitiesList.length,
                                itemBuilder: (context, index) {
                                  final activity = activitiesList[index];
                                  return _buildActivityItem(context, activity);
                                },
                              ),
                              loading: () => const Center(child: ProgressRing()),
                              error: (error, stack) => Center(
                                child: Text('Error loading activities: $error'),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Revenue Chart
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).translate('revenueGrowth'),
                            style: FluentTheme.of(context).typography.subtitle,
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 300,
                            child: LineChart(
                              LineChartData(
                                gridData: const FlGridData(show: true),
                                titlesData: const FlTitlesData(
                                  leftTitles: AxisTitles(
                                    sideTitles: SideTitles(showTitles: true),
                                  ),
                                  bottomTitles: AxisTitles(
                                    sideTitles: SideTitles(showTitles: true),
                                  ),
                                  rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                                  topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                                ),
                                borderData: FlBorderData(show: true),
                                lineBarsData: [
                                  LineChartBarData(
                                    spots: const [
                                      FlSpot(0, 8000),
                                      FlSpot(1, 9500),
                                      FlSpot(2, 11000),
                                      FlSpot(3, 12500),
                                      FlSpot(4, 14000),
                                      FlSpot(5, 15000),
                                    ],
                                    isCurved: true,
                                    color: Colors.purple,
                                    barWidth: 3,
                                    dotData: const FlDotData(show: true),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(BuildContext context, String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 24, color: color),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: FluentTheme.of(context).typography.body,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: FluentTheme.of(context).typography.title?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(BuildContext context, SharedEconomyActivity activity) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            _getActivityIcon(activity.type),
            size: 20,
            color: _getActivityColor(activity.status),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.description,
                  style: FluentTheme.of(context).typography.body?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _formatDateTime(activity.timestamp),
                  style: FluentTheme.of(context).typography.caption,
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getActivityColor(activity.status).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getActivityColor(activity.status).withValues(alpha: 0.3),
              ),
            ),
            child: Text(
              activity.status,
              style: TextStyle(
                color: _getActivityColor(activity.status),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'NEW_PARTNER':
        return FluentIcons.add_friend;
      case 'LISTING_CREATED':
        return FluentIcons.product_list;
      case 'TRANSACTION':
        return FluentIcons.money;
      default:
        return FluentIcons.info;
    }
  }

  Color _getActivityColor(String status) {
    switch (status) {
      case 'COMPLETED':
        return Colors.green;
      case 'ACTIVE':
        return Colors.blue;
      case 'PENDING':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showAddPartnerDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ContentDialog(
        title: Text(AppLocalizations.of(context).translate('addPartner')),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Add partner form will be implemented here.'),
          ],
        ),
        actions: [
          Button(
            child: Text(AppLocalizations.of(context).translate('cancel')),
            onPressed: () => Navigator.of(context).pop(),
          ),
          FilledButton(
            child: Text(AppLocalizations.of(context).translate('save')),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}
