import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:jaguar_jwt/jaguar_jwt.dart';
import 'package:dotenv/dotenv.dart';
import '../../services/database_service.dart';
import '../middleware/auth_middleware.dart';

class AuthRoutes {
  Router get router {
    final router = Router();

    router.post('/login', _login);
    router.post('/refresh', _refreshToken);
    router.post('/logout', _logout);
    router.get('/me', _getCurrentUser);

    return router;
  }

  Future<Response> _login(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final username = data['username'] as String?;
      final password = data['password'] as String?;

      if (username == null || password == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Username and password are required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Query user from database
      final user = await DatabaseService.instance.queryOne(
        'SELECT * FROM users WHERE username = @username AND password_hash = @password',
        {'username': username, 'password': password}, // In production, hash the password
      );

      if (user == null) {
        return Response.unauthorized(
          jsonEncode({'error': 'Invalid credentials'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Generate JWT tokens
      final env = DotEnv()..load();
      final jwtSecret = env['JWT_SECRET'] ?? 'your-secret-key';
      
      final accessToken = _generateAccessToken(user, jwtSecret);
      final refreshToken = _generateRefreshToken(user, jwtSecret);

      // Update last login time
      await DatabaseService.instance.execute(
        'UPDATE users SET last_login_at = NOW() WHERE id = @id',
        {'id': user['id']},
      );

      return Response.ok(
        jsonEncode({
          'access_token': accessToken,
          'refresh_token': refreshToken,
          'token_type': 'Bearer',
          'expires_in': 3600, // 1 hour
          'user': {
            'id': user['id'],
            'username': user['username'],
            'email': user['email'],
            'role': user['role'],
            'branch_id': user['branch_id'],
          },
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Internal server error'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _refreshToken(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final refreshToken = data['refresh_token'] as String?;

      if (refreshToken == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Refresh token is required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Verify refresh token
      final env = DotEnv()..load();
      final jwtSecret = env['JWT_SECRET'] ?? 'your-secret-key';
      
      final claimSet = verifyJwtHS256Signature(refreshToken, jwtSecret);
      
      if (claimSet.expiry != null && claimSet.expiry!.isBefore(DateTime.now())) {
        return Response.unauthorized(
          jsonEncode({'error': 'Refresh token expired'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Get user from database
      final user = await DatabaseService.instance.queryOne(
        'SELECT * FROM users WHERE id = @id',
        {'id': claimSet.subject},
      );

      if (user == null) {
        return Response.unauthorized(
          jsonEncode({'error': 'User not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Generate new access token
      final newAccessToken = _generateAccessToken(user, jwtSecret);

      return Response.ok(
        jsonEncode({
          'access_token': newAccessToken,
          'token_type': 'Bearer',
          'expires_in': 3600,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.unauthorized(
        jsonEncode({'error': 'Invalid refresh token'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _logout(Request request) async {
    // In a real implementation, you might want to blacklist the token
    return Response.ok(
      jsonEncode({'message': 'Logged out successfully'}),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _getCurrentUser(Request request) async {
    try {
      final userInfo = getUserFromRequest(request);
      final userId = userInfo['user_id'];

      final user = await DatabaseService.instance.queryOne(
        'SELECT id, username, email, role, branch_id, created_at FROM users WHERE id = @id',
        {'id': userId},
      );

      if (user == null) {
        return Response.notFound(
          jsonEncode({'error': 'User not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({'user': user}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Internal server error'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  String _generateAccessToken(Map<String, dynamic> user, String secret) {
    final claimSet = JwtClaim(
      subject: user['id'].toString(),
      issuer: 'auto_parts_erp',
      audience: ['auto_parts_erp_app'],
      expiry: DateTime.now().add(Duration(hours: 1)),
      issuedAt: DateTime.now(),
      otherClaims: {
        'username': user['username'],
        'role': user['role'],
        'branch_id': user['branch_id'],
      },
    );

    return issueJwtHS256(claimSet, secret);
  }

  String _generateRefreshToken(Map<String, dynamic> user, String secret) {
    final claimSet = JwtClaim(
      subject: user['id'].toString(),
      issuer: 'auto_parts_erp',
      audience: ['auto_parts_erp_app'],
      expiry: DateTime.now().add(Duration(days: 30)),
      issuedAt: DateTime.now(),
      otherClaims: {
        'type': 'refresh',
      },
    );

    return issueJwtHS256(claimSet, secret);
  }
}
