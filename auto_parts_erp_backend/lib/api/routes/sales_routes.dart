import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import '../../services/database_service.dart';
import '../middleware/auth_middleware.dart';

class SalesRoutes {
  Router get router {
    final router = Router();

    router.get('/', _getSales);
    router.get('/<id>', _getSale);
    router.post('/', _createSale);
    router.put('/<id>', _updateSale);
    router.delete('/<id>', _deleteSale);
    router.get('/reports/daily', _getDailySalesReport);
    router.get('/reports/monthly', _getMonthlySalesReport);

    return router;
  }

  Future<Response> _getSales(Request request) async {
    try {
      final page = int.tryParse(request.url.queryParameters['page'] ?? '1') ?? 1;
      final limit = int.tryParse(request.url.queryParameters['limit'] ?? '50') ?? 50;
      final startDate = request.url.queryParameters['start_date'];
      final endDate = request.url.queryParameters['end_date'];
      final branchId = request.url.queryParameters['branch_id'];

      final offset = (page - 1) * limit;
      
      String whereClause = 'WHERE 1=1';
      Map<String, dynamic> parameters = {'limit': limit, 'offset': offset};

      if (startDate != null) {
        whereClause += ' AND s.sale_date >= @start_date';
        parameters['start_date'] = startDate;
      }

      if (endDate != null) {
        whereClause += ' AND s.sale_date <= @end_date';
        parameters['end_date'] = endDate;
      }

      if (branchId != null) {
        whereClause += ' AND s.branch_id = @branch_id';
        parameters['branch_id'] = int.parse(branchId);
      }

      final sales = await DatabaseService.instance.query('''
        SELECT s.*, c.name as customer_name, b.name_en as branch_name, u.username
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        JOIN branches b ON s.branch_id = b.id
        JOIN users u ON s.user_id = u.id
        $whereClause
        ORDER BY s.sale_date DESC
        LIMIT @limit OFFSET @offset
      ''', parameters);

      return Response.ok(
        jsonEncode({'sales': sales}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to fetch sales: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getSale(Request request) async {
    try {
      final id = int.tryParse(request.params['id']!);
      if (id == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid sale ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final sale = await DatabaseService.instance.queryOne('''
        SELECT s.*, c.name as customer_name, b.name_en as branch_name, u.username
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        JOIN branches b ON s.branch_id = b.id
        JOIN users u ON s.user_id = u.id
        WHERE s.id = @id
      ''', {'id': id});

      if (sale == null) {
        return Response.notFound(
          jsonEncode({'error': 'Sale not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Get sale items
      final items = await DatabaseService.instance.query('''
        SELECT si.*, p.name_en as product_name, p.name_ar as product_name_ar
        FROM sales_items si
        JOIN products p ON si.product_id = p.id
        WHERE si.sale_id = @sale_id
      ''', {'sale_id': id});

      sale['items'] = items;

      return Response.ok(
        jsonEncode({'sale': sale}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to fetch sale: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _createSale(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final userInfo = getUserFromRequest(request);
      final items = data['items'] as List<dynamic>?;

      if (items == null || items.isEmpty) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Sale items are required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final saleId = await DatabaseService.instance.transaction(() async {
        // Create sale
        final saleResult = await DatabaseService.instance.queryOne('''
          INSERT INTO sales (
            invoice_number, customer_id, branch_id, user_id,
            subtotal, tax_amount, discount_amount, total_amount,
            paid_amount, remaining_amount, payment_method, status, notes
          ) VALUES (
            @invoice_number, @customer_id, @branch_id, @user_id,
            @subtotal, @tax_amount, @discount_amount, @total_amount,
            @paid_amount, @remaining_amount, @payment_method, @status, @notes
          ) RETURNING id
        ''', {
          'invoice_number': data['invoice_number'] ?? 'INV-${DateTime.now().millisecondsSinceEpoch}',
          'customer_id': data['customer_id'],
          'branch_id': userInfo['branch_id'] ?? data['branch_id'],
          'user_id': userInfo['user_id'],
          'subtotal': data['subtotal'] ?? 0.0,
          'tax_amount': data['tax_amount'] ?? 0.0,
          'discount_amount': data['discount_amount'] ?? 0.0,
          'total_amount': data['total_amount'] ?? 0.0,
          'paid_amount': data['paid_amount'] ?? 0.0,
          'remaining_amount': data['remaining_amount'] ?? 0.0,
          'payment_method': data['payment_method'],
          'status': data['status'] ?? 'COMPLETED',
          'notes': data['notes'],
        });

        final saleId = saleResult!['id'];

        // Create sale items and update inventory
        for (final item in items) {
          // Create sale item
          await DatabaseService.instance.execute('''
            INSERT INTO sales_items (
              sale_id, product_id, quantity, unit_price, total_price, discount_amount, notes
            ) VALUES (
              @sale_id, @product_id, @quantity, @unit_price, @total_price, @discount_amount, @notes
            )
          ''', {
            'sale_id': saleId,
            'product_id': item['product_id'],
            'quantity': item['quantity'],
            'unit_price': item['unit_price'],
            'total_price': item['total_price'],
            'discount_amount': item['discount_amount'] ?? 0.0,
            'notes': item['notes'],
          });

          // Update inventory
          await DatabaseService.instance.execute('''
            UPDATE branch_inventory 
            SET quantity = quantity - @quantity, last_updated = NOW()
            WHERE product_id = @product_id AND branch_id = @branch_id
          ''', {
            'product_id': item['product_id'],
            'quantity': item['quantity'],
            'branch_id': userInfo['branch_id'] ?? data['branch_id'],
          });

          // Record inventory movement
          await DatabaseService.instance.execute('''
            INSERT INTO inventory_movements (
              product_id, branch_id, movement_type, quantity,
              reference_type, reference_id, user_id
            ) VALUES (
              @product_id, @branch_id, 'OUT', @quantity,
              'SALE', @sale_id, @user_id
            )
          ''', {
            'product_id': item['product_id'],
            'branch_id': userInfo['branch_id'] ?? data['branch_id'],
            'quantity': -item['quantity'],
            'sale_id': saleId,
            'user_id': userInfo['user_id'],
          });
        }

        return saleId;
      });

      return Response.ok(
        jsonEncode({
          'message': 'Sale created successfully',
          'sale_id': saleId,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to create sale: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _updateSale(Request request) async {
    try {
      if (!hasAnyRole(request, ['admin', 'manager'])) {
        return Response.forbidden(
          jsonEncode({'error': 'Insufficient permissions'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final id = int.tryParse(request.params['id']!);
      if (id == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid sale ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final affectedRows = await DatabaseService.instance.execute('''
        UPDATE sales SET
          status = @status,
          notes = @notes,
          updated_at = NOW()
        WHERE id = @id
      ''', {
        'id': id,
        'status': data['status'],
        'notes': data['notes'],
      });

      if (affectedRows == 0) {
        return Response.notFound(
          jsonEncode({'error': 'Sale not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({'message': 'Sale updated successfully'}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to update sale: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _deleteSale(Request request) async {
    try {
      if (!hasRole(request, 'admin')) {
        return Response.forbidden(
          jsonEncode({'error': 'Admin access required'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final id = int.tryParse(request.params['id']!);
      if (id == null) {
        return Response.badRequest(
          body: jsonEncode({'error': 'Invalid sale ID'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Update status to CANCELLED instead of deleting
      final affectedRows = await DatabaseService.instance.execute(
        'UPDATE sales SET status = \'CANCELLED\', updated_at = NOW() WHERE id = @id',
        {'id': id},
      );

      if (affectedRows == 0) {
        return Response.notFound(
          jsonEncode({'error': 'Sale not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({'message': 'Sale cancelled successfully'}),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to cancel sale: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getDailySalesReport(Request request) async {
    try {
      final date = request.url.queryParameters['date'] ?? DateTime.now().toIso8601String().split('T')[0];
      final branchId = request.url.queryParameters['branch_id'];

      String whereClause = 'WHERE DATE(s.sale_date) = @date AND s.status = \'COMPLETED\'';
      Map<String, dynamic> parameters = {'date': date};

      if (branchId != null) {
        whereClause += ' AND s.branch_id = @branch_id';
        parameters['branch_id'] = int.parse(branchId);
      }

      final report = await DatabaseService.instance.queryOne('''
        SELECT 
          COUNT(*) as total_sales,
          SUM(s.total_amount) as total_revenue,
          SUM(s.tax_amount) as total_tax,
          SUM(s.discount_amount) as total_discount,
          AVG(s.total_amount) as average_sale
        FROM sales s
        $whereClause
      ''', parameters);

      // Get top selling products
      final topProducts = await DatabaseService.instance.query('''
        SELECT p.name_en, p.name_ar, SUM(si.quantity) as total_quantity,
               SUM(si.total_price) as total_revenue
        FROM sales s
        JOIN sales_items si ON s.id = si.sale_id
        JOIN products p ON si.product_id = p.id
        $whereClause
        GROUP BY p.id, p.name_en, p.name_ar
        ORDER BY total_quantity DESC
        LIMIT 10
      ''', parameters);

      return Response.ok(
        jsonEncode({
          'date': date,
          'summary': report,
          'top_products': topProducts,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to generate daily sales report: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getMonthlySalesReport(Request request) async {
    try {
      final month = request.url.queryParameters['month'] ?? DateTime.now().month.toString().padLeft(2, '0');
      final year = request.url.queryParameters['year'] ?? DateTime.now().year.toString();
      final branchId = request.url.queryParameters['branch_id'];

      String whereClause = 'WHERE EXTRACT(MONTH FROM s.sale_date) = @month AND EXTRACT(YEAR FROM s.sale_date) = @year AND s.status = \'COMPLETED\'';
      Map<String, dynamic> parameters = {'month': int.parse(month), 'year': int.parse(year)};

      if (branchId != null) {
        whereClause += ' AND s.branch_id = @branch_id';
        parameters['branch_id'] = int.parse(branchId);
      }

      final report = await DatabaseService.instance.queryOne('''
        SELECT 
          COUNT(*) as total_sales,
          SUM(s.total_amount) as total_revenue,
          SUM(s.tax_amount) as total_tax,
          SUM(s.discount_amount) as total_discount,
          AVG(s.total_amount) as average_sale
        FROM sales s
        $whereClause
      ''', parameters);

      // Get daily breakdown
      final dailyBreakdown = await DatabaseService.instance.query('''
        SELECT 
          DATE(s.sale_date) as sale_date,
          COUNT(*) as daily_sales,
          SUM(s.total_amount) as daily_revenue
        FROM sales s
        $whereClause
        GROUP BY DATE(s.sale_date)
        ORDER BY sale_date
      ''', parameters);

      return Response.ok(
        jsonEncode({
          'month': month,
          'year': year,
          'summary': report,
          'daily_breakdown': dailyBreakdown,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to generate monthly sales report: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}
