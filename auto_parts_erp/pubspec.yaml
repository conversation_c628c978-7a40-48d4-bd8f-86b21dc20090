name: auto_parts_erp
description: A comprehensive Flutter Desktop ERP system for auto parts companies.
publish_to: 'none' 
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI Framework
  fluent_ui: ^4.8.0
  syncfusion_flutter_datagrid: ^25.1.35
  
  # State Management
  flutter_riverpod: ^2.4.9

  # Storage
  shared_preferences: ^2.2.2
  riverpod_annotation: ^2.3.3

  # Local Database (SQLite)
  drift: ^2.13.0
  sqlite3_flutter_libs: ^0.5.18
  path_provider: ^2.1.1
  path: ^1.8.3

  # Networking
  dio: ^5.4.0

  # Analytics & Charts
  fl_chart: ^0.66.0

  # PDF & Printing
  pdf: ^3.10.4
  printing: ^5.11.1

  # QR Code
  qr_flutter: ^4.1.0

  # Localization
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

  # Routing
  go_router: ^12.1.1

  # Others
  get_it: ^7.6.4
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  
  flutter_lints: ^3.0.1

  # Code Generators
  build_runner: ^2.4.7
  riverpod_generator: ^2.3.9
  drift_dev: ^2.13.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/fonts/
    - assets/translations/

  
