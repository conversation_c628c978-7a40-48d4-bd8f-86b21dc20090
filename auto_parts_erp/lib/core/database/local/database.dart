import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

// Import all table definitions
import 'tables/users_table.dart';
import 'tables/branches_table.dart';
import 'tables/products_table.dart';
import 'tables/categories_table.dart';
import 'tables/brands_table.dart';
import 'tables/inventory_table.dart';
import 'tables/sales_table.dart';
import 'tables/purchases_table.dart';
import 'tables/customers_table.dart';
import 'tables/suppliers_table.dart';
import 'tables/sync_queue_table.dart';

part 'database.g.dart';

@DriftDatabase(tables: [
  Users,
  Branches,
  Products,
  Categories,
  Brands,
  ProductOemNumbers,
  ProductCarCompatibility,
  BranchInventory,
  InventoryMovements,
  Sales,
  SalesItems,
  Purchases,
  PurchaseItems,
  Customers,
  Suppliers,
  SyncQueue,
])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (Migrator m) async {
      await m.createAll();
      await _insertInitialData();
    },
  );

  Future<void> _insertInitialData() async {
    // Insert default branch
    await into(branches).insert(BranchesCompanion.insert(
      nameEn: 'Main Branch',
      nameAr: 'الفرع الرئيسي',
      address: Value('Main Office'),
    ));

    // Insert default admin user
    await into(users).insert(UsersCompanion.insert(
      username: 'admin',
      email: Value('<EMAIL>'),
      passwordHash: 'admin', // In production, this should be hashed
      role: const Value('admin'),
      branchId: Value(1),
    ));

    // Insert default categories
    await batch((batch) {
      batch.insertAll(categories, [
        CategoriesCompanion.insert(nameEn: 'Engine Parts', nameAr: 'قطع المحرك'),
        CategoriesCompanion.insert(nameEn: 'Brake System', nameAr: 'نظام الفرامل'),
        CategoriesCompanion.insert(nameEn: 'Oils & Fluids', nameAr: 'الزيوت والسوائل'),
        CategoriesCompanion.insert(nameEn: 'Tires', nameAr: 'الإطارات'),
        CategoriesCompanion.insert(nameEn: 'Batteries', nameAr: 'البطاريات'),
        CategoriesCompanion.insert(nameEn: 'Filters', nameAr: 'الفلاتر'),
      ]);
    });

    // Insert default brands
    await batch((batch) {
      batch.insertAll(brands, [
        BrandsCompanion.insert(nameEn: 'Toyota', nameAr: 'تويوتا'),
        BrandsCompanion.insert(nameEn: 'Honda', nameAr: 'هوندا'),
        BrandsCompanion.insert(nameEn: 'Nissan', nameAr: 'نيسان'),
        BrandsCompanion.insert(nameEn: 'Hyundai', nameAr: 'هيونداي'),
        BrandsCompanion.insert(nameEn: 'Kia', nameAr: 'كيا'),
      ]);
    });
  }

  // User operations
  Future<List<User>> getAllUsers() => select(users).get();
  Future<User?> getUserByUsername(String username) =>
      (select(users)..where((u) => u.username.equals(username))).getSingleOrNull();
  Future<User?> getUserByEmail(String email) =>
      (select(users)..where((u) => u.email.equals(email))).getSingleOrNull();

  // Branch operations
  Future<List<Branch>> getAllBranches() => select(branches).get();
  Future<Branch?> getBranchById(int id) =>
      (select(branches)..where((b) => b.id.equals(id))).getSingleOrNull();

  // Product operations
  Future<List<Product>> getAllProducts() => select(products).get();
  Future<Product?> getProductById(int id) =>
      (select(products)..where((p) => p.id.equals(id))).getSingleOrNull();
  Future<List<Product>> searchProducts(String query) =>
      (select(products)..where((p) => 
        p.nameEn.contains(query) | p.nameAr.contains(query))).get();

  // Category operations
  Future<List<Category>> getAllCategories() => select(categories).get();

  // Brand operations
  Future<List<Brand>> getAllBrands() => select(brands).get();

  // Inventory operations
  Future<List<BranchInventoryData>> getInventoryByBranch(int branchId) =>
      (select(branchInventory)..where((i) => i.branchId.equals(branchId))).get();

  // Sales operations
  Future<List<Sale>> getAllSales() => select(sales).get();
  Future<List<Sale>> getSalesByDateRange(DateTime start, DateTime end) =>
      (select(sales)..where((s) => s.saleDate.isBetweenValues(start, end))).get();

  // Sync operations
  Future<List<SyncQueueData>> getPendingSyncItems() =>
      (select(syncQueue)..where((s) => s.synced.equals(false))).get();
  
  Future<void> markAsSynced(int syncId) =>
      (update(syncQueue)..where((s) => s.id.equals(syncId)))
          .write(SyncQueueCompanion(synced: Value(true)));


}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'auto_parts_erp.db'));
    return NativeDatabase.createInBackground(file);
  });
}
