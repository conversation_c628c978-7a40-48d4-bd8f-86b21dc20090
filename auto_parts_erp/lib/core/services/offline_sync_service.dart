import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../database/database_provider.dart';
import '../network/api_client.dart';
import '../constants/app_constants.dart';

// Offline sync service provider
final offlineSyncServiceProvider = Provider<OfflineSyncService>((ref) {
  final database = ref.watch(databaseProvider);
  final apiClient = ref.watch(apiClientProvider);
  return OfflineSyncService(database, apiClient);
});

// Connectivity provider
final connectivityProvider = StreamProvider<ConnectivityResult>((ref) {
  return Connectivity().onConnectivityChanged.map((results) => results.first);
});

// Sync status provider
final syncStatusProvider = StateNotifierProvider<SyncStatusNotifier, SyncStatus>((ref) {
  final syncService = ref.watch(offlineSyncServiceProvider);
  final connectivity = ref.watch(connectivityProvider);
  return SyncStatusNotifier(syncService, connectivity);
});

class OfflineSyncService {
  final AppDatabase _database;
  final ApiClient _apiClient;
  late Timer _syncTimer;
  bool _isOnline = false;
  bool _isSyncing = false;

  OfflineSyncService(this._database, this._apiClient) {
    _initializeConnectivityListener();
    _startPeriodicSync();
  }

  void _initializeConnectivityListener() {
    Connectivity().onConnectivityChanged.listen((results) {
      final result = results.first;
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;
      
      if (!wasOnline && _isOnline) {
        // Just came online, trigger sync
        _triggerSync();
      }
    });
  }

  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (_isOnline && !_isSyncing) {
        _triggerSync();
      }
    });
  }

  Future<void> _triggerSync() async {
    if (_isSyncing) return;
    
    _isSyncing = true;
    try {
      await fullSync();
    } finally {
      _isSyncing = false;
    }
  }

  // Full bidirectional sync
  Future<SyncResult> fullSync() async {
    if (!_isOnline) {
      return SyncResult.error('No internet connection');
    }

    try {
      // Step 1: Push local changes to server
      final pushResult = await _pushLocalChanges();
      if (!pushResult.success) {
        return pushResult;
      }

      // Step 2: Pull server changes
      final pullResult = await _pullServerChanges();
      if (!pullResult.success) {
        return pullResult;
      }

      // Step 3: Resolve conflicts if any
      await _resolveConflicts();

      // Step 4: Update last sync timestamp
      await _updateLastSyncTime();

      return SyncResult.success(
        'Sync completed successfully. ${pushResult.message}, ${pullResult.message}'
      );
    } catch (e) {
      return SyncResult.error('Sync failed: $e');
    }
  }

  // Push local changes to server
  Future<SyncResult> _pushLocalChanges() async {
    try {
      final pendingOperations = await _database.getPendingSyncOperations();
      
      if (pendingOperations.isEmpty) {
        return SyncResult.success('No local changes to push');
      }

      int successCount = 0;
      int failureCount = 0;

      for (final operation in pendingOperations) {
        try {
          final success = await _pushSingleOperation(operation);
          if (success) {
            await _database.markOperationAsSynced(operation.id);
            successCount++;
          } else {
            failureCount++;
          }
        } catch (e) {
          failureCount++;
          // TODO: Implement proper logging
        }
      }

      return SyncResult.success(
        'Pushed $successCount operations, $failureCount failed'
      );
    } catch (e) {
      return SyncResult.error('Failed to push local changes: $e');
    }
  }

  // Push single operation to server
  Future<bool> _pushSingleOperation(SyncOperation operation) async {
    try {
      final data = operation.data != null ? jsonDecode(operation.data!) : null;
      
      switch (operation.tableName) {
        case 'products':
          return await _pushProductOperation(operation.operationType, operation.recordId, data);
        case 'sales':
          return await _pushSaleOperation(operation.operationType, operation.recordId, data);
        case 'purchases':
          return await _pushPurchaseOperation(operation.operationType, operation.recordId, data);
        case 'inventory':
          return await _pushInventoryOperation(operation.operationType, operation.recordId, data);
        default:
          // TODO: Implement proper logging
          return false;
      }
    } catch (e) {
      // TODO: Implement proper logging
      return false;
    }
  }

  // Pull server changes
  Future<SyncResult> _pullServerChanges() async {
    try {
      final lastSyncTime = await _getLastSyncTime();
      
      final response = await _apiClient.getUpdatedRecords(lastSyncTime);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.data);
        final updates = data['updates'] as Map<String, dynamic>;
        
        int totalUpdates = 0;
        
        for (final entry in updates.entries) {
          final tableName = entry.key;
          final records = entry.value as List<dynamic>;
          
          totalUpdates += await _processServerUpdates(tableName, records);
        }
        
        return SyncResult.success('Pulled $totalUpdates updates from server');
      } else {
        return SyncResult.error('Failed to pull server changes: ${response.statusCode}');
      }
    } catch (e) {
      return SyncResult.error('Failed to pull server changes: $e');
    }
  }

  // Process server updates for a specific table
  Future<int> _processServerUpdates(String tableName, List<dynamic> records) async {
    int count = 0;
    
    for (final record in records) {
      try {
        await _applyServerUpdate(tableName, record);
        count++;
      } catch (e) {
        // TODO: Implement proper logging
      }
    }
    
    return count;
  }

  // Apply a single server update
  Future<void> _applyServerUpdate(String tableName, Map<String, dynamic> record) async {
    // Check for conflicts
    final hasConflict = await _checkForConflict(tableName, record);
    
    if (hasConflict) {
      // Store conflict for later resolution
      await _storeConflict(tableName, record);
      return;
    }
    
    // Apply the update
    switch (tableName) {
      case 'products':
        await _updateLocalProduct(record);
        break;
      case 'sales':
        await _updateLocalSale(record);
        break;
      case 'purchases':
        await _updateLocalPurchase(record);
        break;
      case 'inventory':
        await _updateLocalInventory(record);
        break;
    }
  }

  // Check for conflicts between local and server data
  Future<bool> _checkForConflict(String tableName, Map<String, dynamic> serverRecord) async {
    final recordId = serverRecord['id'];
    final serverUpdatedAt = DateTime.parse(serverRecord['updated_at']);
    
    // Get local record
    final localRecord = await _database.getRecordById(tableName, recordId);
    
    if (localRecord == null) {
      return false; // No conflict if record doesn't exist locally
    }
    
    final localUpdatedAt = localRecord['updated_at'] as DateTime?;
    
    if (localUpdatedAt == null) {
      return false; // No conflict if local record has no timestamp
    }
    
    // Check if local record was modified after server record
    return localUpdatedAt.isAfter(serverUpdatedAt);
  }

  // Store conflict for later resolution
  Future<void> _storeConflict(String tableName, Map<String, dynamic> serverRecord) async {
    await _database.storeConflict(
      tableName: tableName,
      recordId: serverRecord['id'],
      localData: await _database.getRecordById(tableName, serverRecord['id']),
      serverData: serverRecord,
      conflictType: 'UPDATE_CONFLICT',
    );
  }

  // Resolve conflicts using predefined strategies
  Future<void> _resolveConflicts() async {
    final conflicts = await _database.getPendingConflicts();
    
    for (final conflict in conflicts) {
      try {
        await _resolveConflict(conflict);
        await _database.markConflictAsResolved(conflict.id);
      } catch (e) {
        // TODO: Implement proper logging
      }
    }
  }

  // Resolve a single conflict
  Future<void> _resolveConflict(ConflictRecord conflict) async {
    // For now, use server-wins strategy
    // In a real app, you might want to implement more sophisticated conflict resolution
    
    switch (conflict.conflictType) {
      case 'UPDATE_CONFLICT':
        // Apply server data
        await _applyServerUpdate(conflict.tableName, conflict.serverData);
        break;
      case 'DELETE_CONFLICT':
        // Handle delete conflicts
        await _handleDeleteConflict(conflict);
        break;
    }
  }

  // Handle delete conflicts
  Future<void> _handleDeleteConflict(ConflictRecord conflict) async {
    // Implementation depends on business logic
    // For now, keep the local record
  }

  // Update local product from server data
  Future<void> _updateLocalProduct(Map<String, dynamic> data) async {
    // Implementation similar to sync_service.dart
    // But with conflict detection and resolution
  }

  // Update local sale from server data
  Future<void> _updateLocalSale(Map<String, dynamic> data) async {
    // Implementation for sales updates
  }

  // Update local purchase from server data
  Future<void> _updateLocalPurchase(Map<String, dynamic> data) async {
    // Implementation for purchase updates
  }

  // Update local inventory from server data
  Future<void> _updateLocalInventory(Map<String, dynamic> data) async {
    // Implementation for inventory updates
  }

  // Push product operation to server
  Future<bool> _pushProductOperation(String operationType, int recordId, Map<String, dynamic>? data) async {
    try {
      switch (operationType) {
        case 'CREATE':
          final response = await _apiClient.createProduct(data!);
          return response.statusCode == 201;
        case 'UPDATE':
          final response = await _apiClient.updateProduct(recordId, data!);
          return response.statusCode == 200;
        case 'DELETE':
          final response = await _apiClient.deleteProduct(recordId);
          return response.statusCode == 200;
        default:
          return false;
      }
    } catch (e) {
      return false;
    }
  }

  // Push sale operation to server
  Future<bool> _pushSaleOperation(String operationType, int recordId, Map<String, dynamic>? data) async {
    // Similar implementation for sales
    return true; // Placeholder
  }

  // Push purchase operation to server
  Future<bool> _pushPurchaseOperation(String operationType, int recordId, Map<String, dynamic>? data) async {
    // Similar implementation for purchases
    return true; // Placeholder
  }

  // Push inventory operation to server
  Future<bool> _pushInventoryOperation(String operationType, int recordId, Map<String, dynamic>? data) async {
    // Similar implementation for inventory
    return true; // Placeholder
  }

  // Get last sync time from preferences
  Future<DateTime> _getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final timestamp = prefs.getInt(AppConstants.keyLastSyncTime);
    
    if (timestamp != null) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    }
    
    // Default to 30 days ago if no previous sync
    return DateTime.now().subtract(const Duration(days: 30));
  }

  // Update last sync time
  Future<void> _updateLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(AppConstants.keyLastSyncTime, DateTime.now().millisecondsSinceEpoch);
  }

  // Queue operation for sync
  Future<void> queueOperation(String operationType, String tableName, int recordId, Map<String, dynamic>? data) async {
    await _database.queueSyncOperation(
      operationType: operationType,
      tableName: tableName,
      recordId: recordId,
      data: data != null ? jsonEncode(data) : null,
    );
    
    // If online, try to sync immediately
    if (_isOnline && !_isSyncing) {
      _triggerSync();
    }
  }

  void dispose() {
    _syncTimer.cancel();
  }
}

// Sync status notifier
class SyncStatusNotifier extends StateNotifier<SyncStatus> {
  final OfflineSyncService _syncService;
  final AsyncValue<ConnectivityResult> _connectivity;

  SyncStatusNotifier(this._syncService, this._connectivity) : super(const SyncStatus()) {
    _connectivity.whenData((connectivity) {
      state = state.copyWith(isOnline: connectivity != ConnectivityResult.none);
    });
  }

  Future<void> manualSync() async {
    state = state.copyWith(isLoading: true, message: 'Syncing...');
    
    final result = await _syncService.fullSync();
    
    state = state.copyWith(
      isLoading: false,
      message: result.message,
      lastSync: result.success ? DateTime.now() : state.lastSync,
    );
  }
}

// Data models
class SyncStatus {
  final bool isLoading;
  final bool isOnline;
  final String? message;
  final DateTime? lastSync;
  final int pendingOperations;

  const SyncStatus({
    this.isLoading = false,
    this.isOnline = false,
    this.message,
    this.lastSync,
    this.pendingOperations = 0,
  });

  SyncStatus copyWith({
    bool? isLoading,
    bool? isOnline,
    String? message,
    DateTime? lastSync,
    int? pendingOperations,
  }) {
    return SyncStatus(
      isLoading: isLoading ?? this.isLoading,
      isOnline: isOnline ?? this.isOnline,
      message: message ?? this.message,
      lastSync: lastSync ?? this.lastSync,
      pendingOperations: pendingOperations ?? this.pendingOperations,
    );
  }
}

class SyncResult {
  final bool success;
  final String message;

  const SyncResult.success(this.message) : success = true;
  const SyncResult.error(this.message) : success = false;
}

class SyncOperation {
  final int id;
  final String operationType;
  final String tableName;
  final int recordId;
  final String? data;
  final DateTime createdAt;

  SyncOperation({
    required this.id,
    required this.operationType,
    required this.tableName,
    required this.recordId,
    this.data,
    required this.createdAt,
  });
}

class ConflictRecord {
  final int id;
  final String tableName;
  final int recordId;
  final Map<String, dynamic> localData;
  final Map<String, dynamic> serverData;
  final String conflictType;
  final DateTime createdAt;

  ConflictRecord({
    required this.id,
    required this.tableName,
    required this.recordId,
    required this.localData,
    required this.serverData,
    required this.conflictType,
    required this.createdAt,
  });
}
