
import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:drift/drift.dart' hide Column;
import 'package:auto_parts_erp/core/localization/localization_service.dart';
import 'package:auto_parts_erp/core/database/database_provider.dart';
import 'package:auto_parts_erp/core/database/local/database.dart';
import 'package:auto_parts_erp/core/utils/validators.dart';
import 'product_list_screen.dart';

class AddProductScreen extends ConsumerStatefulWidget {
  const AddProductScreen({super.key});

  @override
  ConsumerState<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends ConsumerState<AddProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameEnController = TextEditingController();
  final _nameArController = TextEditingController();
  final _skuController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _costPriceController = TextEditingController();
  final _sellingPriceController = TextEditingController();

  bool _isLoading = false;

  @override
  void dispose() {
    _nameEnController.dispose();
    _nameArController.dispose();
    _skuController.dispose();
    _barcodeController.dispose();
    _costPriceController.dispose();
    _sellingPriceController.dispose();
    super.dispose();
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final productRepo = ref.read(productRepositoryProvider);

      await productRepo.createProduct(
        ProductsCompanion.insert(
          nameEn: _nameEnController.text.trim(),
          nameAr: _nameArController.text.trim(),
          sku: Value(_skuController.text.trim().isEmpty
              ? null : _skuController.text.trim()),
          barcode: Value(_barcodeController.text.trim().isEmpty
              ? null : _barcodeController.text.trim()),
          costPrice: Value(double.tryParse(_costPriceController.text) ?? 0.0),
          sellingPrice: Value(double.tryParse(_sellingPriceController.text) ?? 0.0),
        ),
      );

      if (mounted) {
        // Refresh products list
        ref.invalidate(productsProvider);

        // Show success message
        displayInfoBar(context, builder: (context, close) {
          return InfoBar(
            title: Text(AppLocalizations.of(context).translate('success')),
            content: Text(AppLocalizations.of(context).translate('productAddedSuccessfully')),
            severity: InfoBarSeverity.success,
            onClose: close,
          );
        });

        // Navigate back
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        displayInfoBar(context, builder: (context, close) {
          return InfoBar(
            title: Text(AppLocalizations.of(context).translate('error')),
            content: Text('${AppLocalizations.of(context).translate('failedToAddProduct')}: $e'),
            severity: InfoBarSeverity.error,
            onClose: close,
          );
        });
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldPage(
      header: PageHeader(
        title: Text(AppLocalizations.of(context).translate('addProduct')),
        commandBar: Row(
          children: [
            FilledButton(
              onPressed: _isLoading ? null : _saveProduct,
              child: _isLoading
                  ? Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(
                          width: 16,
                          height: 16,
                          child: ProgressRing(),
                        ),
                        const SizedBox(width: 8),
                        Text(AppLocalizations.of(context).translate('saving')),
                      ],
                    )
                  : Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(FluentIcons.save),
                        const SizedBox(width: 8),
                        Text(AppLocalizations.of(context).translate('save')),
                      ],
                    ),
            ),
            const SizedBox(width: 8),
            Button(
              onPressed: _isLoading ? null : () => context.pop(),
              child: Text(AppLocalizations.of(context).translate('cancel')),
            ),
          ],
        ),
      ),
      content: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Information Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.of(context).translate('basicInformation'),
                        style: FluentTheme.of(context).typography.subtitle,
                      ),
                      const SizedBox(height: 16),

                      // Product Names
                      Row(
                        children: [
                          Expanded(
                            child: InfoLabel(
                              label: '${AppLocalizations.of(context).translate('nameEn')} *',
                              child: TextFormBox(
                                controller: _nameEnController,
                                placeholder: AppLocalizations.of(context).translate('enterProductNameEn'),
                                validator: (value) => Validators.validateRequired(value,
                                    fieldName: AppLocalizations.of(context).translate('nameEn')),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: InfoLabel(
                              label: '${AppLocalizations.of(context).translate('nameAr')} *',
                              child: TextFormBox(
                                controller: _nameArController,
                                placeholder: AppLocalizations.of(context).translate('enterProductNameAr'),
                                validator: (value) => Validators.validateRequired(value,
                                    fieldName: AppLocalizations.of(context).translate('nameAr')),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // SKU and Barcode
                      Row(
                        children: [
                          Expanded(
                            child: InfoLabel(
                              label: AppLocalizations.of(context).translate('sku'),
                              child: TextFormBox(
                                controller: _skuController,
                                placeholder: AppLocalizations.of(context).translate('enterSku'),
                                validator: Validators.validateSKU,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: InfoLabel(
                              label: AppLocalizations.of(context).translate('barcode'),
                              child: TextFormBox(
                                controller: _barcodeController,
                                placeholder: AppLocalizations.of(context).translate('enterBarcode'),
                                validator: Validators.validateBarcode,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Prices
                      Row(
                        children: [
                          Expanded(
                            child: InfoLabel(
                              label: AppLocalizations.of(context).translate('costPrice'),
                              child: TextFormBox(
                                controller: _costPriceController,
                                placeholder: '0.00',
                                validator: Validators.validatePrice,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: InfoLabel(
                              label: AppLocalizations.of(context).translate('sellingPrice'),
                              child: TextFormBox(
                                controller: _sellingPriceController,
                                placeholder: '0.00',
                                validator: Validators.validatePrice,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
